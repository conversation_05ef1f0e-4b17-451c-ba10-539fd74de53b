import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  AppBar,
  Toolbar,
  Badge
} from '@mui/material';
import {
  Computer,
  Storage,
  Memory,
  Schedule,
  LiveTv,
  Person,
  Email,
  Assignment,
  Timer,
  YouTube,
  Circle
} from '@mui/icons-material';
import io from 'socket.io-client';
import moment from 'moment';

const SOCKET_URL = 'http://localhost:3001';

function App() {
  const [backends, setBackends] = useState([]);
  const [statistics, setStatistics] = useState({
    totalBackends: 0,
    onlineBackends: 0
  });
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    // Inisialisasi Socket.IO connection
    const socket = io(SOCKET_URL);

    socket.on('connect', () => {
      console.log('🔌 Connected to monitoring server');
      setConnectionStatus('connected');
    });

    socket.on('disconnect', () => {
      console.log('🔌 Disconnected from monitoring server');
      setConnectionStatus('disconnected');
    });

    socket.on('all_backends', (data) => {
      console.log('📊 Received all backends data:', data);
      setBackends(data);
      setLastUpdate(moment().toISOString());
    });

    socket.on('monitoring_data', (data) => {
      console.log('📊 Received monitoring data:', data);
      setBackends(prev => {
        const updated = prev.filter(b => b.backendId !== data.backendId);
        return [...updated, data].sort((a, b) => a.name.localeCompare(b.name));
      });
      setLastUpdate(moment().toISOString());
    });

    socket.on('statistics', (data) => {
      console.log('📈 Received statistics:', data);
      setStatistics(data);
    });

    socket.on('backend_disconnected', ({ backendId }) => {
      console.log('🔌 Backend disconnected:', backendId);
      setBackends(prev => prev.filter(b => b.backendId !== backendId));
    });

    // Cleanup on unmount
    return () => {
      socket.disconnect();
    };
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'success';
      case 'offline': return 'error';
      default: return 'warning';
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const duration = moment.duration(seconds, 'seconds');
    const days = Math.floor(duration.asDays());
    const hours = duration.hours();
    const minutes = duration.minutes();
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh', bgcolor: 'transparent' }}>
      {/* Header */}
      <AppBar position="static" sx={{ bgcolor: 'rgba(0,0,0,0.8)', backdropFilter: 'blur(10px)' }}>
        <Toolbar>
          <Computer sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Real-time Backend Monitoring Dashboard
          </Typography>
          <Badge badgeContent={statistics.onlineBackends} color="success" sx={{ mr: 2 }}>
            <Circle color={connectionStatus === 'connected' ? 'success' : 'error'} />
          </Badge>
          <Typography variant="body2">
            {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 3, pb: 3 }}>
        {/* Statistics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
              <CardContent>
                <Typography color="white" gutterBottom>
                  Total Backends
                </Typography>
                <Typography variant="h4" color="white">
                  {statistics.totalBackends}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
              <CardContent>
                <Typography color="white" gutterBottom>
                  Online Backends
                </Typography>
                <Typography variant="h4" color="white">
                  {statistics.onlineBackends}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
              <CardContent>
                <Typography color="white" gutterBottom>
                  Last Update
                </Typography>
                <Typography variant="body1" color="white">
                  {lastUpdate ? moment(lastUpdate).format('HH:mm:ss') : 'Never'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
              <CardContent>
                <Typography color="white" gutterBottom>
                  Connection Status
                </Typography>
                <Chip 
                  label={connectionStatus} 
                  color={connectionStatus === 'connected' ? 'success' : 'error'}
                  variant="filled"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Connection Status Alert */}
        {connectionStatus === 'disconnected' && (
          <Alert severity="error" sx={{ mb: 3 }}>
            Disconnected from monitoring server. Trying to reconnect...
          </Alert>
        )}

        {/* Backend Cards */}
        {backends.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center', bgcolor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
            <Typography variant="h6" color="white">
              No backends connected yet
            </Typography>
            <Typography color="rgba(255,255,255,0.7)">
              Waiting for backend data...
            </Typography>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {backends.map((backend) => (
              <Grid item xs={12} lg={6} xl={4} key={backend.backendId}>
                <Card sx={{ 
                  bgcolor: 'rgba(255,255,255,0.1)', 
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.3)'
                  }
                }}>
                  <CardContent>
                    {/* Header */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" color="white" sx={{ fontWeight: 'bold' }}>
                        {backend.name}
                      </Typography>
                      <Chip 
                        label={backend.status} 
                        color={getStatusColor(backend.status)}
                        size="small"
                        icon={<Circle />}
                      />
                    </Box>

                    {/* Basic Info */}
                    <List dense>
                      <ListItem>
                        <ListItemIcon><Person sx={{ color: 'white' }} /></ListItemIcon>
                        <ListItemText 
                          primary="Email" 
                          secondary={backend.email}
                          primaryTypographyProps={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.875rem' }}
                          secondaryTypographyProps={{ color: 'white' }}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Assignment sx={{ color: 'white' }} /></ListItemIcon>
                        <ListItemText 
                          primary="Order ID" 
                          secondary={backend.orderId}
                          primaryTypographyProps={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.875rem' }}
                          secondaryTypographyProps={{ color: 'white' }}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Timer sx={{ color: 'white' }} /></ListItemIcon>
                        <ListItemText 
                          primary="Remaining Duration" 
                          secondary={formatDuration(backend.remainingDuration)}
                          primaryTypographyProps={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.875rem' }}
                          secondaryTypographyProps={{ color: 'white' }}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><LiveTv sx={{ color: 'white' }} /></ListItemIcon>
                        <ListItemText 
                          primary="Live Status" 
                          secondary={
                            <Chip 
                              label={backend.liveActive ? 'LIVE' : 'OFFLINE'} 
                              color={backend.liveActive ? 'error' : 'default'}
                              size="small"
                            />
                          }
                          primaryTypographyProps={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.875rem' }}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><YouTube sx={{ color: 'white' }} /></ListItemIcon>
                        <ListItemText 
                          primary="YouTube Tokens" 
                          secondary={backend.youtubeTokensRemaining}
                          primaryTypographyProps={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.875rem' }}
                          secondaryTypographyProps={{ color: 'white' }}
                        />
                      </ListItem>
                    </List>

                    <Divider sx={{ my: 2, bgcolor: 'rgba(255,255,255,0.2)' }} />

                    {/* System Resources */}
                    <Typography variant="subtitle2" color="white" sx={{ mb: 1, fontWeight: 'bold' }}>
                      System Resources
                    </Typography>
                    
                    {/* CPU Usage */}
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="rgba(255,255,255,0.7)">
                          CPU Usage
                        </Typography>
                        <Typography variant="body2" color="white">
                          {backend.cpuUsage}%
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={backend.cpuUsage} 
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: backend.cpuUsage > 80 ? '#f44336' : backend.cpuUsage > 60 ? '#ff9800' : '#4caf50'
                          }
                        }}
                      />
                    </Box>

                    {/* Memory Usage */}
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="rgba(255,255,255,0.7)">
                          Memory
                        </Typography>
                        <Typography variant="body2" color="white">
                          {formatBytes(backend.memoryInfo.used)} / {formatBytes(backend.memoryInfo.total)}
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={backend.memoryInfo.percentage} 
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: backend.memoryInfo.percentage > 80 ? '#f44336' : backend.memoryInfo.percentage > 60 ? '#ff9800' : '#4caf50'
                          }
                        }}
                      />
                    </Box>

                    {/* Disk Usage */}
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="rgba(255,255,255,0.7)">
                          Disk
                        </Typography>
                        <Typography variant="body2" color="white">
                          {formatBytes(backend.diskInfo.used)} / {formatBytes(backend.diskInfo.total)}
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={backend.diskInfo.percentage} 
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: backend.diskInfo.percentage > 80 ? '#f44336' : backend.diskInfo.percentage > 60 ? '#ff9800' : '#4caf50'
                          }
                        }}
                      />
                    </Box>

                    {/* Active Schedules */}
                    {backend.activeSchedules && backend.activeSchedules.length > 0 && (
                      <>
                        <Divider sx={{ my: 2, bgcolor: 'rgba(255,255,255,0.2)' }} />
                        <Typography variant="subtitle2" color="white" sx={{ mb: 1, fontWeight: 'bold' }}>
                          <Schedule sx={{ mr: 1, fontSize: '1rem' }} />
                          Active Schedules ({backend.activeSchedules.length})
                        </Typography>
                        <Box sx={{ maxHeight: 100, overflow: 'auto' }}>
                          {backend.activeSchedules.map((schedule, index) => (
                            <Chip 
                              key={index}
                              label={schedule}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5, bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                            />
                          ))}
                        </Box>
                      </>
                    )}

                    {/* Last Update */}
                    <Box sx={{ mt: 2, textAlign: 'right' }}>
                      <Typography variant="caption" color="rgba(255,255,255,0.5)">
                        Last update: {moment(backend.lastUpdate).format('HH:mm:ss')}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Container>
    </Box>
  );
}

export default App;
