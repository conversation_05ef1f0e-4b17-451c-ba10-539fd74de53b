{"name": "monitoring-dashboard", "version": "1.0.0", "description": "Real-time monitoring dashboard", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "moment": "^2.29.4", "recharts": "^2.8.0", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}