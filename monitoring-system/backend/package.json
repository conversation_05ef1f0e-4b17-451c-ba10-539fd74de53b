{"name": "monitoring-server", "version": "1.0.0", "description": "Real-time monitoring server for multiple backends", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "uuid": "^9.0.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["monitoring", "realtime", "websocket"], "author": "Your Name", "license": "MIT"}