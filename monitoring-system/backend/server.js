const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Storage untuk data dari multiple backends
const backendData = new Map();
const connectionHistory = [];

// Fungsi untuk membersihkan data lama (lebih dari 5 menit offline)
const cleanupOldData = () => {
  const fiveMinutesAgo = moment().subtract(5, 'minutes');
  
  for (const [backendId, data] of backendData.entries()) {
    if (moment(data.lastUpdate).isBefore(fiveMinutesAgo)) {
      console.log(`🗑️ Removing old data for backend: ${backendId}`);
      backendData.delete(backendId);
      
      // Broadcast removal to all clients
      io.emit('backend_disconnected', { backendId });
    }
  }
};

// Cleanup setiap 1 menit
setInterval(cleanupOldData, 60000);

// API endpoint untuk menerima data dari backend
app.post('/api/monitor/data', (req, res) => {
  try {
    const {
      backendId,
      name,
      email,
      orderId,
      remainingDuration,
      liveActive,
      cpuUsage,
      youtubeTokensRemaining,
      diskInfo,
      memoryInfo,
      activeSchedules
    } = req.body;

    // Validasi data yang diperlukan
    if (!backendId || !name) {
      return res.status(400).json({
        success: false,
        message: 'backendId and name are required'
      });
    }

    const timestamp = moment().toISOString();
    
    // Struktur data yang akan disimpan
    const monitoringData = {
      backendId,
      name,
      email: email || 'N/A',
      orderId: orderId || 'N/A',
      remainingDuration: remainingDuration || 0,
      liveActive: liveActive || false,
      cpuUsage: cpuUsage || 0,
      youtubeTokensRemaining: youtubeTokensRemaining || 0,
      diskInfo: {
        total: diskInfo?.total || 0,
        used: diskInfo?.used || 0,
        free: diskInfo?.free || 0,
        percentage: diskInfo?.percentage || 0
      },
      memoryInfo: {
        total: memoryInfo?.total || 0,
        used: memoryInfo?.used || 0,
        free: memoryInfo?.free || 0,
        percentage: memoryInfo?.percentage || 0
      },
      activeSchedules: activeSchedules || [],
      lastUpdate: timestamp,
      status: 'online'
    };

    // Simpan atau update data
    const isNewBackend = !backendData.has(backendId);
    backendData.set(backendId, monitoringData);

    // Log untuk debugging
    console.log(`📊 Data received from backend: ${name} (${backendId})`);
    
    if (isNewBackend) {
      console.log(`🆕 New backend connected: ${name}`);
      connectionHistory.push({
        backendId,
        name,
        action: 'connected',
        timestamp
      });
    }

    // Broadcast data ke semua client yang terhubung
    io.emit('monitoring_data', monitoringData);
    
    // Kirim daftar semua backend yang aktif
    const allBackends = Array.from(backendData.values());
    io.emit('all_backends', allBackends);

    res.json({
      success: true,
      message: 'Data received successfully',
      timestamp,
      totalBackends: backendData.size
    });

  } catch (error) {
    console.error('❌ Error processing monitoring data:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// API untuk mendapatkan semua data backend
app.get('/api/monitor/backends', (req, res) => {
  try {
    const allBackends = Array.from(backendData.values());
    res.json({
      success: true,
      data: allBackends,
      total: allBackends.length,
      timestamp: moment().toISOString()
    });
  } catch (error) {
    console.error('❌ Error getting backends data:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// API untuk mendapatkan history koneksi
app.get('/api/monitor/history', (req, res) => {
  try {
    res.json({
      success: true,
      data: connectionHistory.slice(-50), // 50 history terakhir
      timestamp: moment().toISOString()
    });
  } catch (error) {
    console.error('❌ Error getting connection history:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);
  
  // Kirim semua data backend yang aktif ke client baru
  const allBackends = Array.from(backendData.values());
  socket.emit('all_backends', allBackends);
  
  // Kirim statistik
  socket.emit('statistics', {
    totalBackends: backendData.size,
    onlineBackends: allBackends.filter(b => b.status === 'online').length,
    timestamp: moment().toISOString()
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });

  // Handle request untuk data spesifik backend
  socket.on('request_backend_data', (backendId) => {
    const data = backendData.get(backendId);
    if (data) {
      socket.emit('backend_data', data);
    } else {
      socket.emit('backend_not_found', { backendId });
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: moment().toISOString(),
    activeBackends: backendData.size
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 Monitoring Server running on port ${PORT}`);
  console.log(`📊 WebSocket endpoint: ws://localhost:${PORT}`);
  console.log(`🌐 API endpoint: http://localhost:${PORT}/api/monitor`);
  console.log(`❤️ Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
