import { faYoutube } from '@fortawesome/free-brands-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Trans } from '@lingui/macro';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Switch from '@mui/material/Switch';
import TextField from '@mui/material/TextField';
import React, { useEffect, useState, useRef, useCallback } from 'react'; // Import useCallback
import FormInlineButton from '../../../misc/FormInlineButton';
import MenuItem from '@mui/material/MenuItem';
import moment from 'moment-timezone';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import { styled } from '@mui/material/styles';
import Chip from '@mui/material/Chip';
import RefreshIcon from '@mui/icons-material/Refresh';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

// Modern Calendar Component
const ModernCalendar = ({ selectedDays, onDateChange, onMessage }) => {
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [isRangeSelectMode, setIsRangeSelectMode] = useState(false);
    const [rangeStart, setRangeStart] = useState(null);

    const getDaysInMonth = (date) => {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            days.push(null);
        }

        // Add all days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            days.push(new Date(year, month, day));
        }

        return days;
    };

    const isDateSelected = (date) => {
        if (!date) return false;
        return selectedDays.some(d =>
            d.year === date.getFullYear() &&
            d.month === date.getMonth() + 1 &&
            d.day === date.getDate()
        );
    };

    const isDateInRange = (date) => {
        if (!date || !rangeStart) return false;
        const dateTime = date.getTime();
        const startTime = rangeStart.getTime();
        return dateTime >= startTime;
    };

    const handleDateClick = (date, event) => {
        if (!date) return;

        // Check if date is in the past (before today)
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to start of today
        const clickedDate = new Date(date);
        clickedDate.setHours(0, 0, 0, 0); // Set to start of clicked date

        if (clickedDate < today) {
            onMessage(`❌ Tidak dapat memilih tanggal yang sudah terlewati: ${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`);
            return;
        }

        const newDay = {
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate()
        };

        const isSelected = isDateSelected(date);
        const isCtrlPressed = event.ctrlKey || event.metaKey;

        if (isCtrlPressed || isRangeSelectMode) {
            // Range select mode
            if (!rangeStart) {
                // Set start of range
                setRangeStart(date);
                onMessage(`📍 Mulai range dari tanggal ${newDay.day}/${newDay.month}/${newDay.year}. Klik tanggal akhir untuk menyelesaikan range.`);
            } else {
                // Complete the range
                const startDate = rangeStart;
                const endDate = date;

                // Ensure start is before end
                const actualStart = startDate <= endDate ? startDate : endDate;
                const actualEnd = startDate <= endDate ? endDate : startDate;

                // Generate all dates in range
                const rangeDates = [];
                const currentDate = new Date(actualStart);

                while (currentDate <= actualEnd) {
                    // Check if current date in range is not in the past
                    const rangeDate = new Date(currentDate);
                    rangeDate.setHours(0, 0, 0, 0);

                    if (rangeDate >= today) {
                        const rangeDay = {
                            year: currentDate.getFullYear(),
                            month: currentDate.getMonth() + 1,
                            day: currentDate.getDate()
                        };

                        // Only add if not already selected
                        const exists = selectedDays.some(d =>
                            d.year === rangeDay.year && d.month === rangeDay.month && d.day === rangeDay.day
                        );
                        if (!exists) {
                            rangeDates.push(rangeDay);
                        }
                    }

                    currentDate.setDate(currentDate.getDate() + 1);
                }

                if (rangeDates.length > 0) {
                    const newDays = [...selectedDays, ...rangeDates].sort((a, b) => {
                        const dateA = new Date(a.year, a.month - 1, a.day);
                        const dateB = new Date(b.year, b.month - 1, b.day);
                        return dateA - dateB;
                    });
                    onDateChange(newDays);
                    onMessage(`✅ Range ${rangeDates.length} tanggal berhasil ditambahkan!`);
                } else {
                    onMessage(`ℹ️ Tidak ada tanggal valid dalam range atau semua sudah dipilih!`);
                }

                // Reset range
                setRangeStart(null);
            }
        } else {
            // Single select mode
            if (!isSelected) {
                const newDays = [...selectedDays, newDay].sort((a, b) => {
                    const dateA = new Date(a.year, a.month - 1, a.day);
                    const dateB = new Date(b.year, b.month - 1, b.day);
                    return dateA - dateB;
                });
                onDateChange(newDays);
                onMessage(`✅ Tanggal ${newDay.day}/${newDay.month}/${newDay.year} ditambahkan!`);
            } else {
                onMessage(`⚠️ Tanggal ${newDay.day}/${newDay.month}/${newDay.year} sudah dipilih!`);
            }
        }
    };

    const navigateMonth = (direction) => {
        const newMonth = new Date(currentMonth);
        newMonth.setMonth(currentMonth.getMonth() + direction);
        setCurrentMonth(newMonth);
    };

    const days = getDaysInMonth(currentMonth);
    const monthName = currentMonth.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    return (
        <Box sx={{
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2,
            p: 2,
            backgroundColor: 'rgba(0, 0, 0, 0.6)', // Dark background with 60% transparency
            boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
            color: 'white'
        }}>
            {/* Calendar Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <IconButton onClick={() => navigateMonth(-1)} size="small" sx={{ color: 'white' }}>
                    ←
                </IconButton>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'white' }}>
                    {monthName}
                </Typography>
                <IconButton onClick={() => navigateMonth(1)} size="small" sx={{ color: 'white' }}>
                    →
                </IconButton>
            </Box>

            {/* Range select toggle */}
            <Box sx={{ mb: 2, textAlign: 'center' }}>
                <FormControlLabel
                    control={
                        <Switch
                            checked={isRangeSelectMode}
                            onChange={(e) => {
                                setIsRangeSelectMode(e.target.checked);
                                setRangeStart(null); // Reset range when toggling
                            }}
                            size="small"
                        />
                    }
                    label="Mode Range Select (Ctrl+Click)"
                    sx={{ color: 'white' }}
                />
                {rangeStart && (
                    <Typography variant="caption" display="block" sx={{ color: 'yellow', mt: 1 }}>
                        📍 Range dimulai dari {rangeStart.getDate()}/{rangeStart.getMonth() + 1}/{rangeStart.getFullYear()}
                    </Typography>
                )}
            </Box>

            {/* Day names header */}
            <Box sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(7, 1fr)',
                gap: 1,
                mb: 1
            }}>
                {dayNames.map(dayName => (
                    <Box key={dayName} sx={{
                        textAlign: 'center',
                        fontWeight: 600,
                        color: 'rgba(255, 255, 255, 0.7)',
                        fontSize: '0.875rem'
                    }}>
                        {dayName}
                    </Box>
                ))}
            </Box>

            {/* Calendar grid */}
            <Box sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(7, 1fr)',
                gap: 1
            }}>
                {days.map((date, index) => {
                    const isSelected = isDateSelected(date);
                    const isToday = date && date.toDateString() === new Date().toDateString();

                    // Check if date is in the past (more precise check)
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    const checkDate = date ? new Date(date) : null;
                    if (checkDate) checkDate.setHours(0, 0, 0, 0);
                    const isPast = checkDate && checkDate < today;

                    const isRangeStart = rangeStart && date && date.getTime() === rangeStart.getTime();
                    const isInPotentialRange = rangeStart && date && isDateInRange(date);
                    const isClickable = date && !isPast;

                    return (
                        <Box
                            key={index}
                            onClick={(e) => isClickable ? handleDateClick(date, e) : null}
                            sx={{
                                minHeight: 40,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                cursor: isClickable ? 'pointer' : 'not-allowed',
                                borderRadius: 1,
                                fontSize: '0.875rem',
                                fontWeight: isSelected || isRangeStart ? 600 : 400,
                                backgroundColor: isPast
                                    ? 'rgba(255, 255, 255, 0.05)' // Very dim background for past dates
                                    : isSelected
                                        ? 'primary.main'
                                        : isRangeStart
                                            ? 'warning.main'
                                            : isInPotentialRange && (isRangeSelectMode || rangeStart)
                                                ? 'rgba(255, 193, 7, 0.3)'
                                                : isToday
                                                    ? 'rgba(25, 118, 210, 0.3)'
                                                    : 'transparent',
                                color: isPast
                                    ? 'rgba(255, 255, 255, 0.2)' // Very dim text for past dates
                                    : isSelected
                                        ? 'white'
                                        : isRangeStart
                                            ? 'black'
                                            : isToday
                                                ? 'white'
                                                : 'white',
                                border: isToday && !isSelected && !isRangeStart && !isPast ? '2px solid rgba(25, 118, 210, 0.8)' : 'none',
                                '&:hover': isClickable ? {
                                    backgroundColor: isSelected
                                        ? 'primary.dark'
                                        : isRangeStart
                                            ? 'warning.dark'
                                            : 'rgba(255, 255, 255, 0.1)',
                                    transform: 'scale(1.1)',
                                    transition: 'all 0.2s'
                                } : {},
                                opacity: isPast ? 0.3 : 1,
                                position: 'relative',
                                '&::after': isPast ? {
                                    content: '""',
                                    position: 'absolute',
                                    top: '50%',
                                    left: '10%',
                                    right: '10%',
                                    height: '1px',
                                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                                    transform: 'rotate(-45deg)'
                                } : {}
                            }}
                        >
                            {date ? date.getDate() : ''}
                        </Box>
                    );
                })}
            </Box>

            {/* Instructions */}
            <Typography variant="caption" sx={{ mt: 2, display: 'block', textAlign: 'center', color: 'rgba(255, 255, 255, 0.8)' }}>
                {isRangeSelectMode || rangeStart
                    ? "🎯 Mode Range Select aktif - Klik tanggal pertama, lalu klik tanggal terakhir untuk memilih range"
                    : "💡 Klik tanggal untuk pilih individual, atau aktifkan Range Select untuk pilih blok tanggal"
                }
            </Typography>
            <Typography variant="caption" sx={{ mt: 1, display: 'block', textAlign: 'center', color: 'rgba(255, 193, 7, 0.8)' }}>
                ⚠️ Hanya tanggal hari ini dan masa depan yang dapat dipilih
            </Typography>
        </Box>
    );
};






const id = 'youtube';
const name = 'Youtube Live';
const version = '2.0';
const stream_key_link = 'https://www.youtube.com/live_dashboard';
const description = (
    "Setting live streaming YouTube dengan menambahkan Stream Key yang valid. Jadwalkan berguna untuk mengatur live streaming ke YouTube."
);

const image_copyright = (
    <Trans>
        More about YouTube's copyright{' '}
        <Link color="secondary" target="_blank" href="https://www.youtube.com/howyoutubeworks/policies/copyright/#support-and-troubleshooting">
            here
        </Link>
        .
    </Trans>
);

const author = {
    creator: {
        name: 'upstream.id',
        link: 'https://upstream.id',
    },
    maintainer: {
        name: 'upstream.id',
        link: 'https://upstream.id',
    },
};

const category = 'platform';
const requires = {
    protocols: ['rtmps', 'https'],
    formats: ['flv', 'hls'],
    codecs: {
        audio: ['aac', 'mp3'],
        video: ['h264', 'hevc', 'av1'],
    },
};

const StyledSwitch = styled(Switch)(({ theme }) => ({
    padding: 8,
    '& .MuiSwitch-switchBase': {
        padding: 10,
        '&.Mui-disabled': {
            opacity: 0.3,
        },
    },
    '& .MuiSwitch-thumb': {
        width: 20,
        height: 20,
        boxShadow: 'none',
    },
    '& .MuiSwitch-track': {
        borderRadius: 22 / 2,
        backgroundColor: theme.palette.mode === 'light' ? '#E9E9EA' : '#39393D',
        opacity: 1,
        transition: theme.transitions.create(['background-color'], {
            duration: 500,
        }),
        '&:before, &:after': {
            content: '""',
            position: 'absolute',
            top: '50%',
            transform: 'translateY(-50%)',
            width: 16,
            height: 16,
        },
        '&:before': {
            backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24"><path fill="${encodeURIComponent(
                '#fff',
            )}" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/></svg>')`,
            left: 12,
        },
        '&:after': {
            backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24"><path fill="${encodeURIComponent(
                '#fff',
            )}" d="M19,13H5V11H19V13Z" /></svg>')`,
            right: 12,
        },
    },
}));

const init = (settings) => {
    const defaultChatbotKeyword = "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara Hujan, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.";

    const initSettings = {
        mode: 'rtmps',
        contentId: '',
        stream_key: '',
        stream_name: '',
        primary: true,
        backup: false,
        schedule_enabled: false, // Default Jadwal OFF
        start_time: '',
        end_time: '',
        youtube_api: false, // Default ke false - INI PENTING
        channel: '',
        title: '',
        description: '',
        tags: '',
        privacy: 'public',  // Default value for privacy
        thumbnail: '',
        duration: '',
        userTimezone: moment.tz.guess(),
        chatbot: false, // Tambahkan properti chatbot dengan nilai default false
        chatbot_keyword: defaultChatbotKeyword,
        scheduleRepeat: false,
        repeatSchedule: 'off', // Tambahan baru, default 'off'
        autoGenerateTitle: false, // Tambahan baru
        titlePrompt: 'Generate a creative and engaging title for this content', // Tambahan baru untuk prompt
        manualTitleRotating: false, // Tambahan baru
        manualTitles: '', // Tambahan baru
        customScheduleDates: [], // Menyimpan tanggal-tanggal custom
        repeatCount: 1,
        TokenAPI: 0,
        TokenChatbot: 0,
        ...settings,
    };
    return initSettings;
}

function ServiceIcon(props) {
    return <FontAwesomeIcon icon={faYoutube} style={{ color: '#FF0000' }} {...props} />;
}



function Service(props) {
    const initialSettings = useRef(init(props.settings)); // SIMPAN settings di useRef
    const [duration, setDuration] = useState(initialSettings.current.duration || '');
    const startTimeRef = useRef(initialSettings.current.start_time);
    const [scheduleRepeat, setScheduleRepeat] = useState(initialSettings.current.scheduleRepeat);
    const [repeatCount, setRepeatCount] = useState(initialSettings.current.repeatCount);
    const [streamKeyInfo, setStreamKeyInfo] = useState(null);
    const [youtubeChannelId, setYoutubeChannelId] = useState(initialSettings.current.channel || '');
    const [channels, setChannels] = useState([]);
    const [streamKeys, setStreamKeys] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadedThumbnailUrl, setUploadedThumbnailUrl] = useState(initialSettings.current.thumbnail || '');
    const [isCreatingStreamKey, setIsCreatingStreamKey] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [selectedThumbnail, setSelectedThumbnail] = useState(null);
    const fileInputRef = useRef(null);
    const [openTitleAlert, setOpenTitleAlert] = useState(false);
    const [youtubeAPIEnabled, setYoutubeAPIEnabled] = useState(initialSettings.current.youtube_api); // Default OFF
    const [isYoutubeAPIDisabled, setIsYoutubeAPIDisabled] = useState(true); // Inisialisasi ke true
    const [youtubeChatbotEnabled, setYoutubeChatbotEnabled] = useState(false); // State untuk status Chatbot
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState('error'); // 'success', 'error', 'warning', 'info'

    // Helper function untuk menampilkan snackbar dengan severity yang berbeda
    const showSnackbar = (message, severity = 'error') => {
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setOpenSnackbar(true);
    };

    const [autoGenerateTitle, setAutoGenerateTitle] = useState(initialSettings.current.autoGenerateTitle);
    const [titlePrompt, setTitlePrompt] = useState(initialSettings.current.titlePrompt);
    const [manualTitleRotating, setManualTitleRotating] = useState(initialSettings.current.manualTitleRotating);
    const [selectedDays, setSelectedDays] = useState(
        // Inisialisasi dari settings jika ada, menggunakan moment.js
        (initialSettings.current.customScheduleDates || []).map(dateString => {
            const m = moment(dateString, "YYYY-MM-DD");
            return {
                year: m.year(),
                month: m.month() + 1, // moment().month() dimulai dari 0 (Jan), jadi perlu +1
                day: m.date(),
            };
        })
    );



    const createOutput = useCallback(async (currentSettings) => {
        currentSettings.schedule_repeat = currentSettings.schedule_enabled && youtubeAPIEnabled && currentSettings.scheduleRepeat ? currentSettings.repeatCount : 0;

        if (youtubeAPIEnabled && !currentSettings.title && !currentSettings.autoGenerateTitle && !currentSettings.manualTitleRotating) {
            return [];
        }

        const outputs = [];

        if (youtubeAPIEnabled && !currentSettings.stream_key) {
            return outputs;
        }

        const outputData = {
            stream_key: currentSettings.stream_key,
            stream_name: currentSettings.stream_name,
            start_time: currentSettings.schedule_enabled ? currentSettings.start_time : "",
            end_time: currentSettings.end_time,
            title: currentSettings.title,
            description: currentSettings.description,
            tags: currentSettings.tags,
            thumbnail: currentSettings.thumbnail,
            privacy: currentSettings.privacy,
            youtube_api: youtubeAPIEnabled,
            aicontent: currentSettings.aicontent,
            youtube_channel: currentSettings.channel,
            chatbot: currentSettings.chatbot,
            chatbot_keyword: currentSettings.chatbot_keyword,
            schedule_repeat: currentSettings.schedule_repeat,
            // --- DATA BARU DISERTAKAN DI SINI ---
            repeatSchedule: currentSettings.repeatSchedule,
            autoGenerateTitle: currentSettings.autoGenerateTitle,
            titlePrompt: currentSettings.titlePrompt,
            manualTitleRotating: currentSettings.manualTitleRotating,
            manualTitles: currentSettings.manualTitles,
            customScheduleDates: currentSettings.customScheduleDates,

        };

        

        if (currentSettings.mode === "rtmps") {
            let options = ["-f", "flv"];

            if (props?.skills?.ffmpeg?.version_major >= 6) {
                const codecs = [];
                if (props.skills.codecs.video.includes("hevc")) {
                    codecs.push("hvc1");
                }
                if (props.skills.codecs.video.includes("av1")) {
                    codecs.push("av01");
                }
                if (codecs.length !== 0) {
                    options.push("-rtmp_enhanced_codecs", codecs.join(","));
                }
            }

            if (currentSettings.primary) {
                outputs.push({
                    address: "rtmps://a.rtmp.youtube.com/live2/" + currentSettings.stream_key,
                    options: options.slice(),
                    ...outputData,
                });
            }

            if (currentSettings.backup) {
                outputs.push({
                    address: "rtmps://b.rtmp.youtube.com/live2/" + currentSettings.stream_key,
                    options: options.slice(),
                    ...outputData,
                });
            }
        }
        return outputs;
    }, [props, youtubeAPIEnabled]);

  const validateForm = useCallback(() => {
        if (youtubeAPIEnabled) {
            // Validasi Channel YouTube
            if (!initialSettings.current.channel) {
                showSnackbar('⚠️ Silakan pilih Channel YouTube terlebih dahulu!', 'warning');
                return false;
            }

            // Validasi Stream Key
            if (!initialSettings.current.stream_key) {
                showSnackbar('⚠️ Silakan pilih Stream Key dari daftar yang tersedia!', 'warning');
                return false;
            }

            // Validasi Title berdasarkan mode yang dipilih
            if (!initialSettings.current.title && !autoGenerateTitle && !manualTitleRotating) {
                showSnackbar('⚠️ Judul Live Streaming wajib diisi! Atau aktifkan "Auto Generate Judul" atau "Judul Manual Rotating"', 'warning');
                return false;
            }

            // Validasi Manual Titles jika mode Manual Rotating aktif
            if (manualTitleRotating && !initialSettings.current.manualTitles) {
                showSnackbar('⚠️ Daftar judul manual tidak boleh kosong! Silakan isi minimal satu judul untuk rotasi', 'warning');
                return false;
            }

            // Validasi Custom Prompt jika Auto Generate aktif
            if (autoGenerateTitle && !initialSettings.current.titlePrompt) {
                showSnackbar('⚠️ Prompt untuk generate judul tidak boleh kosong! Silakan isi prompt yang akan digunakan AI', 'warning');
                return false;
            }

            return true;

        } else {
            // Mode Non-YouTube API - hanya validasi Stream Key
            if (!initialSettings.current.stream_key) {
                showSnackbar('⚠️ Stream Key wajib diisi! Silakan masukkan stream key yang valid', 'warning');
                return false;
            }

            return true;
        }
    }, [youtubeAPIEnabled, autoGenerateTitle, manualTitleRotating, showSnackbar]);

  const validatedOnChange = useCallback(async (outputs, currentSettings) => {
        if (validateForm()) {
            props.onChange(outputs, currentSettings);
        }
    }, [props, validateForm]);


    // Fungsi untuk memperbarui settings dan memicu onChange
    const updateSettings = useCallback(async (newSettings) => {
        initialSettings.current = { ...initialSettings.current, ...newSettings }; // Update settings.current secara langsung
        try {
            const outputs = await createOutput(initialSettings.current);
            validatedOnChange(outputs, initialSettings.current); // Kirim settings.current yang terbaru
        } catch (error) {
            console.error("Error creating output:", error);
            showSnackbar('❌ Gagal membuat output streaming. Silakan periksa pengaturan dan coba lagi.', 'error');
        }
    }, [createOutput, validatedOnChange]);

    useEffect(() => {
        setYoutubeAPIEnabled(initialSettings.current.youtube_api);
        setAutoGenerateTitle(initialSettings.current.autoGenerateTitle);
        setTitlePrompt(initialSettings.current.titlePrompt);
        setManualTitleRotating(initialSettings.current.manualTitleRotating);
    }, [initialSettings.current.youtube_api, initialSettings.current.autoGenerateTitle, initialSettings.current.titlePrompt, initialSettings.current.manualTitleRotating]);

// ---> PERBAIKAN useEffect UNTUK KALENDER <---


        
    useEffect(() => {
        const fetchClientConfig = async () => {
            try {
                if (props.upstreamid && props.upstreamid.clientConfig) {
                    const { TokenAPI, TokenChatbot, YoutubeAPI, YoutubeChatbot } = props.upstreamid.clientConfig;

                    initialSettings.current.TokenAPI = TokenAPI || 0;
                    initialSettings.current.TokenChatbot = TokenChatbot || 0;
                    setIsYoutubeAPIDisabled(!YoutubeAPI); // Disable jika YoutubeAPI false
                    setYoutubeChatbotEnabled(YoutubeChatbot);
                } else {
                    console.warn("props.upstreamid atau props.upstreamid.clientConfig tidak tersedia.");
                }
            } catch (error) {
                console.error("Error fetching client config:", error);
            }
        };

        fetchClientConfig();
    }, [props.upstreamid]);

 const fetchStreamKeys = async (channelId) => {
        if (!channelId) return;
        setIsLoading(true);
        try {
            const data = await props.upstreamid.fetchStreamKeys(channelId);

            if (data && Array.isArray(data.stream_keys)) {
                const formattedStreamKeys = data.stream_keys.map(key => ({
                    stream_id: key.stream_id,
                    stream_name: key.stream_name,
                    stream_key: key.actual_stream_key || key.stream_key
                }));
                setStreamKeys(formattedStreamKeys);
                
                // +++ UBAH BAGIAN INI +++
                // Jangan panggil updateSettings di sini.
                // Cukup periksa apakah stream key yang tersimpan saat ini masih valid.
                // Jika tidak, atau jika kosong, kita set ke yang pertama.
                const currentStreamKey = initialSettings.current.stream_key;
                const isCurrentKeyValid = formattedStreamKeys.some(key => key.stream_key === currentStreamKey);

                if (!currentStreamKey || !isCurrentKeyValid) {
                    if (formattedStreamKeys.length > 0) {
                        // Panggil updateSettings HANYA jika perlu mengubah.
                        updateSettings({
                            stream_key: formattedStreamKeys[0].stream_key,
                            stream_name: formattedStreamKeys[0].stream_name
                        });
                    } else {
                        // Jika tidak ada stream key, kosongkan.
                        updateSettings({
                            stream_key: '',
                            stream_name: ''
                        });
                    }
                }
                // +++ AKHIR PERUBAHAN +++

            } else {
                console.error("Data stream keys tidak valid:", data);
                setStreamKeys([]);
                 // Kosongkan juga di form jika data tidak valid
                updateSettings({ stream_key: '', stream_name: '' });
            }
        } catch (error) {
            console.error("Error fetching stream keys:", error?.response?.data || error.message);
            setStreamKeys([]);
            // Kosongkan juga di form jika ada error
            updateSettings({ stream_key: '', stream_name: '' });
        }
        setIsLoading(false);
    };

    const handleRefreshStreamKeys = async () => {
        if (!youtubeChannelId) return;
        setIsRefreshing(true);
        try {
            // ---> PERIKSA DAN PASTIKAN ANDA MEMANGGIL FUNGSI INI <---
            console.log(`[DEBUG] Memanggil props.upstreamid.refreshYoutubeStreamKeys...`);
        const data = await props.upstreamid.refreshYoutubeStreamKeys(youtubeChannelId);
        console.log(`[DEBUG] Data refresh stream keys diterima:`, data);
            
            // Logika untuk memproses data setelah refresh berhasil
            if (data && Array.isArray(data.stream_keys)) {
                const formattedStreamKeys = data.stream_keys.map(key => ({
                    stream_id: key.stream_id,
                    stream_name: key.stream_name,
                    stream_key: key.actual_stream_key || key.stream_key
                }));
                setStreamKeys(formattedStreamKeys);
                showSnackbar('Daftar stream key berhasil diperbarui!', 'success');
            } else {
                setStreamKeys([]);
            }
        } catch (error) {
            console.error("Gagal merefresh stream keys:", error);
            showSnackbar('Gagal memperbarui daftar stream key.', 'error');
        } finally {
            setIsRefreshing(false);
        }
    };


    const createStreamKey = async () => {
        if (!youtubeChannelId) {
            alert("Pilih channel terlebih dahulu");
            return;
        }
        setIsCreatingStreamKey(true);
        try {
            const data = await props.upstreamid.createStreamKey(youtubeChannelId);
            // console.log("Stream key created:", data);
            setStreamKeyInfo(data.stream_key_info);
            // console.log(`digunakan channelId: ${youtubeChannelId}`);

            if (data.stream_key_info && data.stream_key_info.stream_id) {
                const newStreamKey = data.stream_key_info.stream_key;
                const newStreamTitle = data.stream_key_info.stream_name;

                updateSettings({
                    stream_key: newStreamKey,
                    stream_name: newStreamTitle,
                });
                await fetchStreamKeys(youtubeChannelId);
                setStreamKeys((prevKeys) => {
                    const newKey = { stream_id: data.stream_key_info.stream_id, stream_name: newStreamTitle, stream_key: newStreamKey };
                    if (!prevKeys.find(key => key.stream_id === data.stream_key_info.stream_id)) {
                        return [...prevKeys, newKey];
                    }
                    return prevKeys;
                });
            } else {
                console.error("Struktur response tidak sesuai:", data);
                alert("Gagal membuat stream key. Struktur response tidak sesuai.");
            }
        } catch (error) {
            console.error(
                "Error creating stream key:",
                error?.response?.data || error.message
            );
            alert("Gagal membuat stream key. Coba lagi nanti.");
        } finally {
            setIsCreatingStreamKey(false);
        }
    };
    const deleteStreamKey = async (streamId) => {
        try {
            await props.upstreamid.deleteStreamKey(streamId);
            // console.log("Stream key deleted:", streamId);
            fetchStreamKeys(initialSettings.current.channel);
        } catch (error) {
            console.error("Error deleting stream key:", error?.response?.data || error.message);
            alert("Gagal menghapus stream key. Coba lagi nanti.");
        }
    };

    const handleCustomDateChange = useCallback((newDays) => {
        try {
            // Pastikan newDays adalah array yang valid
            if (!Array.isArray(newDays)) {
                console.warn('handleCustomDateChange: newDays is not an array', newDays);
                return;
            }

            setSelectedDays(newDays);

            // Konversi format tanggal untuk disimpan di JSON
            const formattedDates = newDays.map(day => {
                // Validasi struktur day object
                if (!day || typeof day.year !== 'number' || typeof day.month !== 'number' || typeof day.day !== 'number') {
                    console.warn('handleCustomDateChange: Invalid day object', day);
                    return null;
                }
                return `${day.year}-${String(day.month).padStart(2, '0')}-${String(day.day).padStart(2, '0')}`;
            }).filter(date => date !== null); // Filter out invalid dates

            updateSettings({ customScheduleDates: formattedDates });
        } catch (error) {
            console.error('Error in handleCustomDateChange:', error);
            // Fallback: reset ke array kosong jika ada error
            setSelectedDays([]);
            updateSettings({ customScheduleDates: [] });
        }
    }, [updateSettings]);


    useEffect(() => {
        const fetchChannels = async () => {
            setIsLoading(true); // Tambahkan loading state untuk channel
            try {
                const data = await props.upstreamid.FetchYoutubeChannels();
                if (Array.isArray(data)) { // <-- TAMBAHKAN PENGECEKAN INI
                    setChannels(data);
                } else {
                    console.error("Data channel yang diterima bukan array:", data);
                    setChannels([]); // Set ke array kosong jika format salah
                }
            } catch (error) {
                console.error("Gagal mengambil daftar channel:", error);
                setChannels([]); // Set ke array kosong jika ada error
            } finally {
                setIsLoading(false); // Matikan loading state
            }
        };

        if (youtubeAPIEnabled) {
            fetchChannels();
        } else {
            setChannels([]); // Kosongkan daftar channel jika API dinonaktifkan
        }
    }, [youtubeAPIEnabled, props.upstreamid]);

    useEffect(() => {
        if (youtubeChannelId) {
           console.log(`[DEBUG] useEffect terpicu untuk fetchStreamKeys dengan channelId: ${youtubeChannelId}`);

            fetchStreamKeys(youtubeChannelId);
        }
    }, [youtubeChannelId]);

    const handleThumbnailUpload = async (file) => {
        setIsUploading(true);
        try {
            const allowedTypes = ["image/jpeg", "image/png"];
            if (!allowedTypes.includes(file.type)) {
                alert("Hanya file JPG dan PNG yang diperbolehkan.");
                return null;
            }

            const maxSize = 2 * 1024 * 1024;
            if (file.size > maxSize) {
                alert("Ukuran file thumbnail maksimal 2MB.");

                return null;
            }
            const formData = new FormData();
            formData.append("thumbnail", file);
            const data = await props.upstreamid.uploadThumbnail(formData); // Panggil fungsi upload
            // console.log("Respons Lengkap dari Backend:", data); // Tambahkan log di sini
            if (data.thumbnail_url) {
                return data.thumbnail_url;
            } else {
                throw new Error("Backend tidak mengembalikan thumbnail_url!");
            }
        } catch (error) {
            console.error("Error upload thumbnail:", error);
            alert(`Gagal mengunggah thumbnail. Coba lagi nanti: ${error.message}`); // Tampilkan pesan error
            return null;
        } finally {
            setIsUploading(false);
        }
    };
    const handleFileChange = async (event) => {
        const file = event.target.files[0];
        if (file) {
            setSelectedThumbnail(file);

            const thumbnailUrl = await handleThumbnailUpload(file);
            if (thumbnailUrl) {
                setUploadedThumbnailUrl(thumbnailUrl);
                updateSettings({ thumbnail: thumbnailUrl }); // Update settings
            }
        }
    };
    const handleUploadButtonClick = () => {
        fileInputRef.current && fileInputRef.current.click();
    };
    const handleOpenPreview = () => {
        if (uploadedThumbnailUrl) {
            window.open(uploadedThumbnailUrl, '_blank');
        }
    };

const handleChange = (what) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    const changes = { [what]: value };

    // --- LOGIKA UNTUK REPEAT SCHEDULE ---
    if (what === 'repeatSchedule') {
        // Reset selected days jika bukan mode custom
        if (value !== 'custom') {
            setSelectedDays([]);
            changes.customScheduleDates = [];
        }
    }
    // --- AKHIR LOGIKA REPEAT SCHEDULE ---
    
    switch (what) {
        case 'youtube_api':
            onYoutubeAPIEnabledChange(event);
            return;
        case 'autoGenerateTitle':
            setAutoGenerateTitle(value);
            if (value) {
                setManualTitleRotating(false);
                changes.manualTitleRotating = false;
            }
            break;
        case 'titlePrompt':
            setTitlePrompt(value);
            break;
        case 'manualTitleRotating':
            setManualTitleRotating(value);
            if (value) {
                setAutoGenerateTitle(false);
                changes.autoGenerateTitle = false;
            }
            break;
        case 'channel':
            setYoutubeChannelId(value);
            break;
        case 'thumbnail':
            setUploadedThumbnailUrl(value);
            break;
        case 'repeatCount':
            changes[what] = parseInt(value, 10) || 1;
            setRepeatCount(changes[what]);
            break;
        default:
            break;
    }

    updateSettings(changes);
};


    const handleChatbotKeywordChange = (event) => {
        updateSettings({ chatbot_keyword: event.target.value });
    };
    const handleTitleChange = (event) => {
        const value = event.target.value;
        if (youtubeAPIEnabled && !value && !autoGenerateTitle && !manualTitleRotating) {
            setOpenTitleAlert(true);
        } else {
            setOpenTitleAlert(false);
        }
        updateSettings({ title: value });
    };

    const handleTitleAlertClose = () => {
        setOpenTitleAlert(false);
    };
    const handleDurationChange = (event) => {
        const selectedDuration = event.target.value;
        setDuration(selectedDuration);

        const nowUser = moment.tz(initialSettings.current.userTimezone);
        let startTimeUser = moment.tz(initialSettings.current.userTimezone);

        if (startTimeRef.current) {
            startTimeUser = moment.tz(startTimeRef.current, initialSettings.current.userTimezone);
        } else {
            initialSettings.current.start_time = nowUser.format('YYYY-MM-DDTHH:mm');
            startTimeRef.current = initialSettings.current.start_time;
            startTimeUser = moment.tz(initialSettings.current.start_time, initialSettings.current.userTimezone);
        }
        let endTimeUser;
        switch (selectedDuration) {
            case 'week':
                endTimeUser = startTimeUser.clone().add(1, 'week');
                break;
            case 'month':
                endTimeUser = startTimeUser.clone().add(1, 'year');
                break;
            case 'nonstop':
                endTimeUser = startTimeUser.clone().add(1, 'year');
                break;
            default:
                const hourDuration = parseInt(selectedDuration, 10);
                endTimeUser = startTimeUser.clone().add(hourDuration, 'hours');
                break;
        }
        const endTimeFormatted = endTimeUser.format('YYYY-MM-DDTHH:mm');
        updateSettings({ end_time: endTimeFormatted, duration: selectedDuration });
    };
    const handleReset = () => {
        updateSettings({ start_time: '', end_time: '', duration: '' });
        setDuration('');
        startTimeRef.current = null;
    };

    const handleChatbotChange = (event) => {
        updateSettings({ chatbot: event.target.checked });
    };

    const durationOptions = [
        ...Array.from({ length: 24 }, (_, i) => i + 1).map(hour => ({
            value: hour.toString(),
            label: `${hour} Jam`
        })),
        { value: 'week', label: '1 Minggu' },
        { value: 'month', label: '1 Bulan' },
        { value: 'nonstop', label: 'Nonstop (1 Tahun)' }
    ];
    
const repeatScheduleOptions = [
    { value: 'off', label: 'OFF' },
    { value: 'daily', label: 'Setiap Hari' },
    { value: 'weekly', label: 'Setiap Minggu' },
    { value: 'monthly', label: 'Setiap Bulan' },
    { value: 'yearly', label: 'Setiap Tahun' },
    { value: 'custom', label: 'Custom (Pilih Tanggal)' }, // Pilihan baru
];


    const renderDurationSettings = () => (
        <>
            <Grid item xs={12} md={3}>
                <TextField
                    select
                    fullWidth
                    label="Duration"
                    value={duration}
                    onChange={handleDurationChange}
                    variant="outlined"
                    SelectProps={{
                        MenuProps: {
                            PaperProps: {
                                style: {
                                    maxHeight: 200,
                                },
                            },
                        },
                    }}
                >
                    {durationOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                            {option.label}
                        </MenuItem>
                    ))}
                </TextField>
            </Grid>
            <Grid item xs={12} md={4.5}>
                <TextField
                    variant="outlined"
                    fullWidth
                    label="Start Time"
                    type="datetime-local"
                    InputLabelProps={{ shrink: true }}
                    value={initialSettings.current.start_time}
                    onChange={handleChange('start_time')}
                />
            </Grid>
            <Grid item xs={12} md={4.5}>
                <TextField
                    variant="outlined"
                    fullWidth
                    label="End Time"
                    type="datetime-local"
                    InputLabelProps={{ shrink: true }}
                    value={initialSettings.current.end_time}
                    onChange={handleChange('end_time')}
                />
            </Grid>
        </>
    );

    const proChip = (
        <Chip
            label="PRO"
            color="success"
        />
    );

    const handleStreamKeyChange = async (event) => {
        const selectedStreamName = event.target.value;
        const selectedStream = streamKeys.find(key => key.stream_name === selectedStreamName);
        if (selectedStream) {
            updateSettings({ stream_key: selectedStream.stream_key, stream_name: selectedStreamName });
        }
    };

    const onYoutubeAPIEnabledChange = useCallback(async (event) => {
        const newValue = event.target.checked;
        setYoutubeAPIEnabled(newValue);
        updateSettings({ youtube_api: newValue });
    }, [updateSettings]);



    return (
        <Grid container spacing={2}>
            <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">
                   Tanda <span style={{ color: 'red' }}>*</span> wajib diisi
                </Typography>
            </Grid>
            <Grid item xs={12} style={{ display: 'flex', alignItems: 'center' }}>
                <FormControlLabel
                    control={
                        <Switch
                            checked={youtubeAPIEnabled}
                            onChange={onYoutubeAPIEnabledChange}
                            disabled={isYoutubeAPIDisabled}
                        />
                    }
                    label="Youtube Automation"
                />
                {isYoutubeAPIDisabled && proChip}

                {youtubeAPIEnabled && youtubeChatbotEnabled && (
                    <FormControlLabel
                        control={<Switch checked={initialSettings.current.chatbot} onChange={handleChatbotChange} />}
                        label="Chatbot"
                    />
                )}
            </Grid>
            {youtubeAPIEnabled && initialSettings.current.chatbot && youtubeChatbotEnabled && (
                <Grid item xs={12}>
                    <TextField
                        fullWidth
                        label="Chatbot Instructions"
                        placeholder="Masukkan keyword untuk chatbot"
                        value={initialSettings.current.chatbot_keyword}
                        onChange={handleChatbotKeywordChange}
                        multiline
                        rows={2}
                        helperText="Keyword ini akan digunakan untuk memicu chatbot."
                    />
                </Grid>
            )}
            {youtubeAPIEnabled && (props.upstreamid?.clientConfig?.TokenAPI < 10 || props.upstreamid?.clientConfig?.TokenChatbot < 10) && (
                <Grid item xs={12}>
                    <Alert severity="warning">Token Tidak Cukup! Dibutuhkan minimal 10 token untuk 1X Live menggunakan Youtube API</Alert>
                </Grid>
            )}
            {youtubeAPIEnabled ? (
                <>
                    <Grid item xs={12}>
                        <TextField
                            select
                            fullWidth
                            label="Privacy"
                            value={initialSettings.current.privacy}
                            onChange={handleChange('privacy')}
                        >
                            <MenuItem value="public">Public</MenuItem>
                            <MenuItem value="private">Private</MenuItem>
                            <MenuItem value="unlisted">Unlisted</MenuItem>
                        </TextField>
                    </Grid>
                    <Grid item xs={12}>
                        <TextField
                            select
                            fullWidth
                            label={<>Select Channel <span style={{ color: 'red' }}>*</span></>}
                            value={initialSettings.current.channel || ''}
                            onChange={handleChange('channel')}
                            required
                        >
                            {isLoading ? (
                                <MenuItem disabled>Loading...</MenuItem>
                            ) : (
                                channels.map((channel) => (
                                    <MenuItem key={channel.id} value={channel.id}>
                                        <img src={channel.logo || '/default-logo.png'} alt={channel.name || 'Channel'} style={{ width: 24, height: 24, marginRight: 10, borderRadius: '50%' }} onError={(e) => (e.target.src = '/default-logo.png')} />
                                        {channel.name || 'Nama Tidak Tersedia'}
                                    </MenuItem>
                                ))
                            )}
                        </TextField>
                    </Grid>
                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <TextField
                            select
                            fullWidth
                            label={<>Pilih Stream Key <span style={{ color: 'red' }}>*</span></>}
                            value={initialSettings.current.stream_name || ''}
                            onChange={handleStreamKeyChange}
                            required
                            style={{ marginRight: '8px' }}
                        >
                            <MenuItem value="" disabled>
                                Pilih atau buat stream key baru
                            </MenuItem>
                            <MenuItem value="auto" onClick={createStreamKey} disabled={isCreatingStreamKey}>
                                ➕ {isCreatingStreamKey ? "Membuat..." : "Buat Stream Key Otomatis"}
                            </MenuItem>
                            {isLoading ? (
                                <MenuItem disabled>Loading...</MenuItem>
                            ) : (
                                streamKeys.map((key) => (
                                    <MenuItem key={key.stream_id} value={key.stream_name}>
                                        {key.stream_name}
                                    </MenuItem>
                                ))
                            )}
                        </TextField>
                        <Tooltip title="Refresh stream key list">
                            <span>
                                <Button
                                    onClick={handleRefreshStreamKeys}
                                    disabled={isRefreshing || isLoading || !youtubeChannelId}
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                        minWidth: 'auto',
                                        padding: '6px 8px',
                                        border: '1px solid rgba(255, 255, 255, 0.3)',
                                        borderRadius: '4px',
                                        color: 'rgba(255, 255, 255, 0.7)',
                                        '&:hover': {
                                            border: '1px solid rgba(255, 255, 255, 0.5)',
                                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                            color: 'rgba(255, 255, 255, 0.9)',
                                        },
                                        '&:disabled': {
                                            border: '1px solid rgba(255, 255, 255, 0.1)',
                                            color: 'rgba(255, 255, 255, 0.3)',
                                        }
                                    }}
                                >
                                    {isRefreshing ? <CircularProgress size={16} color="inherit" /> : <RefreshIcon fontSize="small" />}
                                </Button>
                            </span>
                        </Tooltip>
                    </Grid>
                    <Grid item xs={12}>
                        <TextField
                            fullWidth
                            label={<>Title <span style={{ color: 'red' }}>*</span></>}
                            value={initialSettings.current.title}
                            onChange={handleTitleChange}
                            required
                        />
                    </Grid>
                    <Grid item xs={12}><TextField fullWidth label="Description" value={initialSettings.current.description} onChange={handleChange('description')} multiline rows={4} /></Grid>
                    <Grid item xs={12}>
                        <Box display="flex" alignItems="center" gap={1}>
                            <TextField
                                label="Thumbnail URL"
                                value={uploadedThumbnailUrl}
                                onChange={handleChange('thumbnail')}
                                variant="outlined"
                                size="small"
                                style={{ flexGrow: 1 }}
                            />
                            <Button
                                variant="outlined"
                                component="span"
                                onClick={handleUploadButtonClick}
                                disabled={isUploading}
                                startIcon={<CloudUploadIcon />}
                                size="small"
                                style={{ whiteSpace: 'nowrap', lineHeight: '2.7' }}
                            >
                                Upload
                                {isUploading && <CircularProgress size={20} />}
                            </Button>
                            <input
                                type="file"
                                accept="image/*"
                                onChange={handleFileChange}
                                style={{ display: 'none' }}
                                ref={fileInputRef}
                                disabled={isUploading}
                            />
                        </Box>
                    </Grid>
                    {uploadedThumbnailUrl && (
                        <Grid item xs={12}>
                            <Button variant="outlined" onClick={handleOpenPreview} size="small" disabled={isUploading}>
                                Lihat Preview
                            </Button>
                        </Grid>
                    )}
                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <FormControlLabel
                            control={<Switch checked={initialSettings.current.schedule_enabled} onChange={handleChange('schedule_enabled')} />}
                            label="Jadwalkan"
                        />
                        {initialSettings.current.schedule_enabled && (
                            <Button variant="outlined" onClick={handleReset} size="small">
                                Reset
                            </Button>
                        )}
                    </Grid>
                </>
            ) : (
                <>
                    <Grid item xs={12} md={9}>
                        <TextField
                            variant="outlined"
                            fullWidth
                            label={<>Stream key <span style={{ color: 'red' }}>*</span></>}
                            value={initialSettings.current.stream_key}
                            onChange={handleChange('stream_key')}
                            required
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <FormInlineButton target="blank" href={stream_key_link} component="a">
                            YT Studio
                        </FormInlineButton>
                    </Grid>
                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <FormControlLabel
                            control={<Switch checked={initialSettings.current.schedule_enabled} onChange={handleChange('schedule_enabled')} />}
                            label="Jadwalkan" />
                        {initialSettings.current.schedule_enabled && (
                            <Button variant="outlined" onClick={handleReset} size="small">
                                Reset
                            </Button>
                        )}
                    </Grid>
                </>
            )}

            {/* Duration Settings - Show when schedule is enabled */}
            {initialSettings.current.schedule_enabled && (
                <>
                    <Grid item xs={12} md={3}>
                        <TextField
                            select
                            fullWidth
                            label="Duration"
                            value={duration}
                            onChange={handleDurationChange}
                            variant="outlined"
                            SelectProps={{
                                MenuProps: {
                                    PaperProps: {
                                        style: {
                                            maxHeight: 200,
                                        },
                                    },
                                },
                            }}
                        >
                            {durationOptions.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </TextField>
                    </Grid>
                    <Grid item xs={12} md={4.5}>
                        <TextField
                            variant="outlined"
                            fullWidth
                            label="Start Time"
                            type="datetime-local"
                            InputLabelProps={{ shrink: true }}
                            value={initialSettings.current.start_time}
                            onChange={handleChange('start_time')}
                        />
                    </Grid>
                    <Grid item xs={12} md={4.5}>
                        <TextField
                            variant="outlined"
                            fullWidth
                            label="End Time"
                            type="datetime-local"
                            InputLabelProps={{ shrink: true }}
                            value={initialSettings.current.end_time}
                            onChange={handleChange('end_time')}
                        />
                    </Grid>
                </>
            )}

{initialSettings.current.schedule_enabled && youtubeAPIEnabled && (
    <>
        <Grid item xs={12}>
            <TextField
                select
                fullWidth
                label="Ulangi Jadwal"
                value={initialSettings.current.repeatSchedule}
                onChange={handleChange('repeatSchedule')}
            >
                {repeatScheduleOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label}
                    </MenuItem>
                ))}
            </TextField>
        </Grid>

{/* Modern Custom Date Picker */}
{initialSettings.current.repeatSchedule === 'custom' && (
    <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ color: 'white', fontWeight: 600 }}>
            📅 Pilih Tanggal Custom
        </Typography>
        <Typography variant="body2" gutterBottom sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            Gunakan Range Select atau Ctrl + Click untuk memilih blok tanggal sekaligus
        </Typography>

        <Box sx={{
            border: '0.1px solid',
            borderColor: 'primary.light',
            borderRadius: 2,
            p: 2,
            mt: 1,
            backgroundColor: 'rgba(39, 39, 39, 0.6)', // Dark background with 60% transparency
            boxShadow: '0 4px 20px rgba(37, 37, 37, 0.45)',
            color: 'white'
        }}>
            {/* Modern Interactive Calendar */}
            <Box sx={{ mb: 3 }}>
                <ModernCalendar
                    selectedDays={selectedDays}
                    onDateChange={handleCustomDateChange}
                    onMessage={(message, type = 'info') => {
                        // Tentukan severity berdasarkan tipe pesan dari calendar
                        let severity = 'info';
                        if (message.includes('✅') || message.includes('berhasil')) {
                            severity = 'success';
                        } else if (message.includes('⚠️') || message.includes('tidak bisa')) {
                            severity = 'warning';
                        } else if (message.includes('❌') || message.includes('error')) {
                            severity = 'error';
                        }
                        showSnackbar(message, severity);
                    }}
                />
            </Box>



            {/* Quick Date Selection Buttons */}
            <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: 'white' }}>
                    🚀 Quick Select:
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                        size="small"
                        variant="outlined"
                        onClick={() => {
                            const today = new Date();
                            today.setHours(0, 0, 0, 0); // Set to start of today
                            const dates = [];

                            for (let i = 0; i < 7; i++) {
                                const date = new Date(today);
                                date.setDate(today.getDate() + i);

                                // Only include dates from today onwards
                                if (date >= today) {
                                    const newDay = {
                                        year: date.getFullYear(),
                                        month: date.getMonth() + 1,
                                        day: date.getDate()
                                    };

                                    // Only add if not exists
                                    const exists = selectedDays.some(d =>
                                        d.year === newDay.year && d.month === newDay.month && d.day === newDay.day
                                    );
                                    if (!exists) {
                                        dates.push(newDay);
                                    }
                                }
                            }

                            if (dates.length > 0) {
                                const newDays = [...selectedDays, ...dates].sort((a, b) => {
                                    const dateA = new Date(a.year, a.month - 1, a.day);
                                    const dateB = new Date(b.year, b.month - 1, b.day);
                                    return dateA - dateB;
                                });
                                handleCustomDateChange(newDays);
                                showSnackbar(`✅ ${dates.length} tanggal (hari ini + 6 hari kedepan) ditambahkan!`, 'success');
                            } else {
                                showSnackbar(`ℹ️ Semua tanggal dalam 7 hari kedepan sudah dipilih!`, 'info');
                            }
                        }}
                    >
                        7 Hari Kedepan
                    </Button>

                    <Button
                        size="small"
                        variant="outlined"
                        onClick={() => {
                            const today = new Date();
                            today.setHours(0, 0, 0, 0); // Set to start of today
                            const dates = [];

                            // Hanya hari kerja (Senin-Jumat) dari hari ini kedepan
                            for (let i = 0; i < 30; i++) {
                                const date = new Date(today);
                                date.setDate(today.getDate() + i);
                                const dayOfWeek = date.getDay();

                                // Only include dates from today onwards and skip weekend
                                if (date >= today && dayOfWeek !== 0 && dayOfWeek !== 6) {
                                    const newDay = {
                                        year: date.getFullYear(),
                                        month: date.getMonth() + 1,
                                        day: date.getDate()
                                    };

                                    // Only add if not exists
                                    const exists = selectedDays.some(d =>
                                        d.year === newDay.year && d.month === newDay.month && d.day === newDay.day
                                    );
                                    if (!exists) {
                                        dates.push(newDay);
                                    }
                                }
                            }

                            if (dates.length > 0) {
                                const newDays = [...selectedDays, ...dates].sort((a, b) => {
                                    const dateA = new Date(a.year, a.month - 1, a.day);
                                    const dateB = new Date(b.year, b.month - 1, b.day);
                                    return dateA - dateB;
                                });
                                handleCustomDateChange(newDays);
                                showSnackbar(`✅ ${dates.length} hari kerja (dari hari ini) ditambahkan!`, 'success');
                            } else {
                                showSnackbar(`ℹ️ Semua hari kerja dalam 30 hari kedepan sudah dipilih!`, 'info');
                            }
                        }}
                    >
                        Hari Kerja (30 Hari)
                    </Button>

                    <Button
                        size="small"
                        variant="outlined"
                        onClick={() => {
                            const today = new Date();
                            today.setHours(0, 0, 0, 0); // Set to start of today
                            const dates = [];

                            for (let i = 0; i < 30; i++) {
                                const date = new Date(today);
                                date.setDate(today.getDate() + i);

                                // Only include dates from today onwards
                                if (date >= today) {
                                    const newDay = {
                                        year: date.getFullYear(),
                                        month: date.getMonth() + 1,
                                        day: date.getDate()
                                    };

                                    // Only add if not exists
                                    const exists = selectedDays.some(d =>
                                        d.year === newDay.year && d.month === newDay.month && d.day === newDay.day
                                    );
                                    if (!exists) {
                                        dates.push(newDay);
                                    }
                                }
                            }

                            if (dates.length > 0) {
                                const newDays = [...selectedDays, ...dates].sort((a, b) => {
                                    const dateA = new Date(a.year, a.month - 1, a.day);
                                    const dateB = new Date(b.year, b.month - 1, b.day);
                                    return dateA - dateB;
                                });
                                handleCustomDateChange(newDays);
                                showSnackbar(`✅ ${dates.length} tanggal (30 hari dari hari ini) ditambahkan!`, 'success');
                            } else {
                                showSnackbar(`ℹ️ Semua tanggal dalam 30 hari kedepan sudah dipilih!`, 'info');
                            }
                        }}
                    >
                        30 Hari Kedepan
                    </Button>

                    {selectedDays.length > 0 && (
                        <Button
                            size="small"
                            variant="outlined"
                            color="error"
                            onClick={() => {
                                handleCustomDateChange([]);
                                showSnackbar('🗑️ Semua tanggal telah dihapus!', 'info');
                            }}
                        >
                            Hapus Semua
                        </Button>
                    )}
                </Box>
            </Box>

            {/* Statistics and Selected Dates Display */}
            {selectedDays.length > 0 && (
                <Box>
                    {/* Statistics */}
                    <Box sx={{
                        display: 'flex',
                        gap: 2,
                        mb: 2,
                        flexWrap: 'wrap',
                        justifyContent: 'center'
                    }}>
                        <Chip
                            label={`📊 Total: ${selectedDays.length} hari`}
                            color="primary"
                            variant="outlined"
                        />
                        <Chip
                            label={`📅 Hari Kerja: ${selectedDays.filter(day => {
                                const date = new Date(day.year, day.month - 1, day.day);
                                const dayOfWeek = date.getDay();
                                return dayOfWeek !== 0 && dayOfWeek !== 6;
                            }).length}`}
                            color="success"
                            variant="outlined"
                        />
                        <Chip
                            label={`🎉 Weekend: ${selectedDays.filter(day => {
                                const date = new Date(day.year, day.month - 1, day.day);
                                const dayOfWeek = date.getDay();
                                return dayOfWeek === 0 || dayOfWeek === 6;
                            }).length}`}
                            color="warning"
                            variant="outlined"
                        />
                    </Box>

                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: 'white' }}>
                        📋 Tanggal Terpilih ({selectedDays.length}):
                    </Typography>
                    <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1,
                        mt: 1,
                        maxHeight: '250px',
                        overflowY: 'auto',
                        p: 2,
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        borderRadius: 2,
                        border: '1px solid',
                        borderColor: 'rgba(255, 255, 255, 0.3)'
                    }}>
                        {selectedDays.map((day, index) => {
                            const date = new Date(day.year, day.month - 1, day.day);
                            const dayName = date.toLocaleDateString('id-ID', { weekday: 'short' });
                            const monthName = date.toLocaleDateString('id-ID', { month: 'short' });
                            const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                            const isToday = date.toDateString() === new Date().toDateString();

                            return (
                                <Chip
                                    key={`${day.year}-${day.month}-${day.day}`}
                                    label={`${dayName}, ${day.day} ${monthName} ${day.year}${isToday ? ' (Hari ini)' : ''}`}
                                    onDelete={() => {
                                        const newDays = selectedDays.filter((_, i) => i !== index);
                                        handleCustomDateChange(newDays);
                                        showSnackbar(`🗑️ Tanggal ${day.day}/${day.month}/${day.year} dihapus!`, 'info');
                                    }}
                                    color={isToday ? "secondary" : isWeekend ? "warning" : "primary"}
                                    variant="filled"
                                    sx={{
                                        fontWeight: 500,
                                        fontSize: '0.875rem',
                                        '&:hover': {
                                            transform: 'scale(1.05)',
                                            transition: 'transform 0.2s',
                                            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                                        }
                                    }}
                                />
                            );
                        })}
                    </Box>
                </Box>
            )}

            {selectedDays.length === 0 && (
                <Box sx={{
                    textAlign: 'center',
                    py: 3,
                    color: 'rgba(255, 255, 255, 0.6)'
                }}>
                    <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                        🗓️ Belum ada tanggal yang dipilih
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                        Gunakan kalender di atas, range select, atau quick select
                    </Typography>
                </Box>
            )}
        </Box>
    </Grid>
)}

        <Grid item xs={12}>
            <FormControlLabel
                control={<Switch checked={autoGenerateTitle} onChange={handleChange('autoGenerateTitle')} />}
                label="Auto Generate Judul"
            />
            <FormControlLabel
                control={<Switch checked={manualTitleRotating} onChange={handleChange('manualTitleRotating')} disabled={autoGenerateTitle} />}
                label="Judul Manual Rotating"
            />
        </Grid>

        {autoGenerateTitle && (
            <Grid item xs={12}>
                <TextField
                    variant="outlined"
                    fullWidth
                    label="Prompt untuk Generate Judul"
                    value={titlePrompt}
                    onChange={(e) => updateSettings({ titlePrompt: e.target.value })}
                    multiline
                    rows={3}
                    placeholder="Masukkan prompt untuk menghasilkan judul yang menarik..."
                    helperText="Prompt ini akan digunakan oleh AI untuk menghasilkan judul yang sesuai dengan konten Anda"
                    sx={{
                        '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                            '& fieldset': {
                                borderColor: 'rgba(255, 255, 255, 0.3)',
                            },
                            '&:hover fieldset': {
                                borderColor: 'rgba(255, 255, 255, 0.5)',
                            },
                            '&.Mui-focused fieldset': {
                                borderColor: 'primary.main',
                            },
                        },
                        '& .MuiInputLabel-root': {
                            color: 'rgba(255, 255, 255, 0.7)',
                        },
                        '& .MuiFormHelperText-root': {
                            color: 'rgba(255, 255, 255, 0.6)',
                        },
                    }}
                />
            </Grid>
        )}
        {manualTitleRotating && (
            <Grid item xs={12}>
                <TextField
                    fullWidth
                    label="Daftar Judul (pisahkan dengan baris baru)"
                    value={initialSettings.current.manualTitles}
                    onChange={handleChange('manualTitles')}
                    multiline
                    rows={4}
                    placeholder={"Judul 1\nJudul 2\nJudul 3"}
                />
            </Grid>
        )}
    </>
)}

            <Snackbar
                open={openTitleAlert}
                autoHideDuration={5000}
                onClose={handleTitleAlertClose}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert onClose={handleTitleAlertClose} severity="warning" sx={{ width: '100%' }}>
                    Judul Live Streaming wajib diisi jika menggunakan YouTube API!
                </Alert>
            </Snackbar>
            <Snackbar
                open={openSnackbar}
                autoHideDuration={3000}
                onClose={() => setOpenSnackbar(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert onClose={() => setOpenSnackbar(false)} severity={snackbarSeverity} sx={{ width: '100%' }}>
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </Grid>
    );
}

Service.defaultProps = {
    settings: {},
    skills: {},
    metadata: {},
    streams: [],
    onChange: function (output, settings) { },
    upstreamid: null,
};

export { author, category, Service as component, description, ServiceIcon as icon, id, image_copyright, name, requires, stream_key_link, version };