import PropTypes from 'prop-types';
import React, { useCallback } from 'react'; // Import useCallback
import { useNavigate, useParams } from 'react-router-dom';

import { Trans, t } from '@lingui/macro';
import { useLingui } from '@lingui/react';
import Backdrop from '@mui/material/Backdrop';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import TextField from '@mui/material/TextField';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import makeStyles from '@mui/styles/makeStyles';
import styled from 'styled-components'; // Import styled-components
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';

import NotifyContext from '../../contexts/Notify';
import EncodingSelect from '../../misc/EncodingSelect';
import FilterSelect from '../../misc/FilterSelect';
import Paper from '../../misc/Paper';
import PaperFooter from '../../misc/PaperFooter';
import PaperHeader from '../../misc/PaperHeader';
import TabPanel from '../../misc/TabPanel';
import TabsVerticalGrid from '../../misc/TabsVerticalGrid';
import LimitsControl from '../../misc/controls/Limits';
import ProcessControl from '../../misc/controls/Process';
import SourceControl from '../../misc/controls/Source';
import H from '../../utils/help';
import * as M from '../../utils/metadata';
import Services from './Services';
import TabContent from './TabContent';
import * as helper from './helper';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';

const useStyles = makeStyles((theme) => ({
    buttonAbort: {
        marginBottom: '0.3em',
    },
    gridContainer: {
        marginTop: '0.5em',
    },
    buttonGroup: {
        marginTop: '0.5em',
        marginBottom: '-0.5em',
    },
     horizontalMenu: {
        marginBottom: 0,
        marginTop: 0,
    },
    horizontalTabs: {
        display: 'flex',
        justifyContent: 'center',
    },
    horizontalTab: {
        padding: theme.spacing(1, 1), // Kurangi padding vertikal
        margin: theme.spacing(0, 1),
        minWidth: 'auto',
        marginTop: 0,
        fontWeight: 700,
        fontSize: '0.9rem',
        textTransform: 'none',
        border: `3px solid ${theme.palette.divider}`, // Added border
        borderRadius: theme.shape.borderRadius, // Added border radius
        color: theme.palette.text.secondary, // Default text color
        '&.Mui-selected': {
            color: theme.palette.primary.main, // Selected text color
            backgroundColor: theme.palette.action.selected, // Selected background color
        },
    },
    tabLabel: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
    },
    panel: {
        marginTop: theme.spacing(2), // Tambahkan margin top ke panel
        marginLeft: theme.spacing(2), // Tambahkan margin kiri ke panel
        marginRight: theme.spacing(2), // Tambahkan margin kanan ke panel
        marginBottom: theme.spacing(2), // Tambahkan margin bawah ke panel
    },
}));

// Style untuk label Clip Pro
const ClipProLabel = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  background-color: red;
  color: white;
  padding: 2px 5px;
  font-size: 0.7em;
  border-radius: 0 0 5px 0;
  z-index: 1; /* Ensure it's on top */
`;

const ServiceButtonContainer = styled.div`
  position: relative; /* Make container relative for absolute positioning of label */
`;

export default function Add(props) {
    const theme = useTheme();
    const breakpointUpSm = useMediaQuery(theme.breakpoints.up('sm'));
    const classes = useStyles();
    const { i18n } = useLingui();
    const navigate = useNavigate();
    const [$ready, setReady] = React.useState(false);
    const { channelid: _channelid } = useParams();
    const notify = React.useContext(NotifyContext);
    const [$service, setService] = React.useState('');
    // Pastikan youtube_api memiliki nilai default
    const [$settings, setSettings] = React.useState(() => M.initEgressMetadata({}));
    const [$sources, setSources] = React.useState([]);
    const [$localSources, setLocalSources] = React.useState([]);
    const [$filter, setFilter] = React.useState('all');
    const [$tab, setTab] = React.useState('general');
    const [$skills, setSkills] = React.useState(null);
    const [$process, setProcess] = React.useState({});

    // State untuk validasi dan notifikasi
    const [openSnackbar, setOpenSnackbar] = React.useState(false);
    const [snackbarMessage, setSnackbarMessage] = React.useState('');

    const [$metadata, setMetadata] = React.useState({
        name: '',
        description: '',
        license: '',
    });
    const [$saving, setSaving] = React.useState(false);
    const [$invalid, setInvalid] = React.useState(false);

    React.useEffect(() => {
        (async () => {
            await load();
        })();
    }, []);

    React.useEffect(() => {
        if ($invalid === true) {
            navigate('/', { replace: true });
        }
    }, [navigate, $invalid]);

    const load = async () => {
        const channelid = props.upstreamid.SelectChannel(_channelid);
        if (channelid === '' || channelid !== _channelid) {
            setInvalid(true);
            return;
        }

        const skills = await props.upstreamid.Skills();
        setSkills(skills);

        let ingest = await props.upstreamid.GetIngestMetadata(_channelid);
        setMetadata({
            ...$metadata,
            name: ingest.meta.name,
            description: ingest.meta.description,
            license: ingest.license,
        });

        const localSources = [];

        localSources.push('hls+' + ingest.control.hls.storage);

        if (ingest.control.rtmp.enable) {
            localSources.push('rtmp');
        }

        if (ingest.control.srt.enable) {
            localSources.push('srt');
        }

        setLocalSources(localSources);

        setSources(helper.createSourcesFromStreams(ingest.streams));

        setReady(true);
    };

    const handleFilterChange = (event, value) => {
        if (!value) {
            return;
        }

        setFilter(value);
    };

    const handleServiceSelect = (service) => () => {
        if (service.length !== 0) {
            const s = Services.Get(service);
            if (s === null) {
                return;
            }

            const serviceSkills = helper.conflateServiceSkills(s.requires, $skills);

            const profiles = $settings.profiles;
            profiles[0].video = helper.preselectProfile(profiles[0].video, 'video', $sources[0].streams, serviceSkills.codecs.video, $skills);
            profiles[0].audio = helper.preselectProfile(profiles[0].audio, 'audio', $sources[0].streams, serviceSkills.codecs.audio, $skills);

            setSettings(prevSettings => ({ // Gunakan fungsi updater
                ...prevSettings,
                name: s.name,
                profiles: profiles,
            }));

            setTab('general');
        } else {
            setSettings(prevSettings => ({ // Gunakan fungsi updater
                ...prevSettings,
                ...M.initEgressMetadata({}),
            }));
        }

        setService(service);
    };

    const handleServiceChange = useCallback((outputs, settings) => {
        if (!Array.isArray(outputs)) {
            outputs = [outputs];
        }

        setSettings(prevSettings => ({
            ...prevSettings,
            outputs: outputs,
            settings: settings, // PERHATIKAN: Ini menimpa seluruh objek `settings`
        }));
    }, []);

    // Fungsi Validasi Form (Harus didefinisikan sebelum digunakan)
    const validateForm = useCallback(() => {
        // Hanya jalankan validasi jika servicenya adalah youtube
        if ($service === 'youtube') {
            const youtubeAPIEnabled = $settings.settings?.youtube_api;

            if (youtubeAPIEnabled) {
                if (!$settings.settings?.channel) {
                    setSnackbarMessage('Channel wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }

                if (!$settings.settings?.stream_key) {
                    setSnackbarMessage('Stream Key wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }

                if (!$settings.settings?.title) {
                    setSnackbarMessage('Title wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }
                return true;
            } else {
                if (!$settings.settings?.stream_key) {
                    setSnackbarMessage('Stream Key wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }
                return true;
            }
        }
        // Jika bukan youtube, lewati validasi dan kembalikan true
        return true;
    }, [$service, $settings.settings?.youtube_api, $settings.settings?.channel, $settings.settings?.stream_key, $settings.settings?.title]);

    const handleProcessing = (type) => (encoder, decoder) => {
        const profiles = $settings.profiles;

        profiles[0][type].encoder = encoder;
        profiles[0][type].decoder = decoder;

        const streams = M.createOutputStreams($sources, profiles, false);

        let outputs = $settings.outputs;

        service = Services.Get($service);
        if (service !== null) {
            if ('createOutputs' in service) {
                const serviceSkills = helper.conflateServiceSkills(service.requires, $skills);
                outputs = service.createOutputs($settings.settings, serviceSkills, $metadata, streams);
            }
        }

        setSettings(prevSettings => ({ // Gunakan fungsi updater
            ...prevSettings,
            profiles: profiles,
            streams: streams,
            outputs: outputs,
        }));
    };

    const handleProcessingFilter = (type) => (filter) => {
        const profiles = $settings.profiles;

        profiles[0][type].filter = filter;

        setSettings(prevSettings => ({ // Gunakan fungsi updater
            ...prevSettings,
            profiles: profiles,
        }));
    };

    const validatedOnChange = useCallback(async (outputs, currentSettings) => {
        if (validateForm()) {
            props.onChange(outputs, currentSettings);
        }
    }, [props, validateForm]);

    const handleServiceDone = useCallback(async () => {
        setSaving(true);

        // Validasi form
        const isValid = validateForm();

        if (!isValid) {
            setSaving(false);
            return;
        }

        const [global, inputs, outputs] = helper.createInputsOutputs($sources, $settings.profiles, $settings.outputs, false);
        if (inputs.length === 0 || outputs.length === 0) {
            setSaving(false);
            notify.Dispatch('error', 'save:egress:' + $service, "Pastikan Kolom dengan * Telah di Isi."); return;
        }

        const [id, err] = await props.upstreamid.CreateEgress(_channelid, $service, global, inputs, outputs, $settings.control);
        if (err !== null) {
            setSaving(false);
            notify.Dispatch('error', 'save:egress:' + $service, i18n._(t`Failed to create publication service (${err.message})`));
            return;
        }

        await props.upstreamid.SetEgressMetadata(_channelid, id, $settings);

        // Kirim data streaming ke backend hanya jika service adalah YouTube
        if ($service === 'youtube') {
            const formatDateForISO8601 = (dateStr) => {
                if (!dateStr) return ""; // Return empty string if date is null/undefined
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                    console.error("🚨 ERROR: Invalid date format!", dateStr);
                    return ""; // Return empty string if invalid date
                }
                return date.toISOString(); // ISO 8601 format
            };

            // console.log("🔍 Debugging $settings sebelum dikirim:", $settings);

            const rawId = id; // ID dari CreateEgress
            const youtubeChannelId = rawId.split(":").pop(); // Ambil UUID
            const channel = $settings.settings ? $settings.settings.channel : ""; //adalah channelid dari youtube

            const streamData = {
                channel_id: channel || "", //adalah channelid dari youtube, untuk menyimpan di data
                service: $service,
                youtube_channel_id: youtubeChannelId, // Hanya UUID
                stream_key: $settings.settings?.stream_key || "",
                stream_name: $settings.settings?.stream_name || "",
                start_time: $settings.settings?.schedule_enabled
                    ? formatDateForISO8601($settings.settings?.start_time)
                    : "",
                end_time: $settings.settings?.schedule_enabled
                    ? formatDateForISO8601($settings.settings?.end_time)
                    : "",
                title: $settings.settings?.title || "",
                description: $settings.settings?.description || "",
                tags: $settings.settings?.tags || "",
                youtube_api: !!$settings.settings?.youtube_api, // Pastikan boolean
                aicontent: !!$settings.settings?.aicontent, // Ensure it's a boolean
                chatbot: !!$settings.settings?.chatbot, // Ensure it's a boolean
                thumbnail: $settings.settings?.thumbnail || "",
                privacy: $settings.settings?.privacy || "",
                order: $settings.settings?.order || "listed",
                mode: $settings.settings?.mode || "default",
                chatbot_keyword: $settings.settings?.chatbot_keyword || "",
                schedule_repeat: $settings.settings?.schedule_repeat || 0, // Sertakan schedule_repeat
                status_thumbnails: $settings.settings?.status_thumbnails || "",
                repeatSchedule: $settings.settings?.repeatSchedule || 'off',
                autoGenerateTitle: !!$settings.settings?.autoGenerateTitle,
                manualTitleRotating: !!$settings.settings?.manualTitleRotating,
                manualTitles: $settings.settings?.manualTitles || '',
                customScheduleDates: $settings.settings?.customScheduleDates || [], // Tambahan untuk custom calendar dates

            };

            streamData.youtube_channel_id = youtubeChannelId;

            // console.log("Data sebelum fetch:", {
            //     channel_id: streamData.channel_id,
            //     youtube_channel_id: streamData.youtube_channel_id
            // });

            // console.log("Mengirim data ke backend:", streamData); // Debugging

            // Selalu kirim data ke backend, terlepas dari nilai youtube_api
            // console.log("▶️ idyoutubechannel:", channel);
            // console.log("▶️ idappchannel:", youtubeChannelId);

            try {
                const data = await props.upstreamid.CreateYoutubeLivestream(youtubeChannelId, streamData); // Gunakan fungsi dari upstreamid
                // console.log("✅ Data berhasil disimpan:", data);
            } catch (error) {
                console.error("🚨 Error saat mengirim data ke backend:", error);
                notify.Dispatch('error', 'save:egress:' + $service, i18n._(t`Failed to create publication service (${error.message})`));
            }
        }

        let message = 'Publikasi berhasil dibuat';
        if ($settings.name.length !== 0) {
            message = `Publikasi Live "${$settings.name}" berhasil dibuat`;
        }

        setSaving(false);
        notify.Dispatch('success', 'save:egress:' + $service, message);
        navigate(`/${_channelid}/`);
    }, [$service, _channelid, $settings, props.upstreamid, i18n, navigate, notify, validateForm, $sources]);

    const handleServiceName = (event) => {
        const name = event.target.value;

        setSettings(prevSettings => ({ // Gunakan fungsi updater
            ...prevSettings,
            name: name,
        }));
    };

    const handleControlChange = (what) => (control) => {
        setSettings(prevSettings => ({ // Gunakan fungsi updater
            ...prevSettings,
            control: {
                ...prevSettings.control,
                [what]: control,
            },
        }));
    };

    const handleAbort = () => {
        navigate(`/${_channelid}`);
    };

    const handleChangeTab = (event, value) => {
        setTab(value);
    };

    const handleHelp = () => {
        let topic = 'publication-add';

        if ($service !== '') {
            topic = 'publication-' + $tab;
        }

        H(topic);
    };

    if ($ready === false) {
        return null;
    }

    let serviceList = [];

    let ServiceControl = null;
    let serviceSkills = null;

    let service = {};

    const youtubeApiEnabled = props.upstreamid?.clientConfig?.RTMPstreaming === true;
    const isRtmpService = (s) => s.id === 'rtmp'; // Fungsi untuk mengecek apakah service adalah RTMP

    if ($service === '') {
        for (let s of Services.List()) {
            if ($filter !== 'all') {
                if (s.category !== $filter) {
                    continue;
                }
            }

            const Icon = s.icon;
            const isDisabled = !youtubeApiEnabled && isRtmpService(s);
            // TODO: Style Tooltip + Fix Tooltip + Disabled
            if (helper.checkServiceRequirements(s.requires, $skills) === false) {
                serviceList.push(
                    <Grid item xs={12} sm={6} md={3} align="center" key={s.id}>
                        <Tooltip
                            title={
                                <React.Fragment>
                                    <Typography variant="subtitle2">
                                        <Trans>Incompatible</Trans>
                                    </Typography>
                                    <Typography>
                                        <Trans>Check the requirements</Trans>
                                    </Typography>
                                </React.Fragment>
                            }
                            placement="left"
                            arrow
                        >
                            <div>
                                <Button variant="big" disabled>
                                    <div>
                                        <Icon />
                                        <Typography>{s.name}</Typography>
                                    </div>
                                </Button>
                            </div>
                        </Tooltip>
                    </Grid>,
                );
            } else {
                serviceList.push(
                    <Grid item xs={12} align="center" key={s.id}>
                        <ServiceButtonContainer>
                            {/* Conditional rendering of ClipProLabel */}
                            {isDisabled && <ClipProLabel>PRO</ClipProLabel>}
                            <Button
                                variant="big"
                                onClick={!isDisabled ? handleServiceSelect(s.id) : undefined}
                                disabled={isDisabled}
                            >
                                <div>
                                    <Icon />
                                    <Typography>{s.name}</Typography>
                                </div>
                            </Button>
                        </ServiceButtonContainer>
                    </Grid>
                );
            }
        }
    } else {
        service = Services.Get($service);
        if (service === null) {
            return null;
        }

        ServiceControl = service.component;
        serviceSkills = helper.conflateServiceSkills(service.requires, $skills);
    }

    return (
        <React.Fragment>
            <Paper xs={12} md={10}>
                <PaperHeader
                    title={
                        <React.Fragment>
                            {$service === '' && <Trans>Add Publication</Trans>}
                            {$service !== '' && (
                                <React.Fragment>
                                    <Trans>Add: {service.name}</Trans>
                                </React.Fragment>
                            )}
                        </React.Fragment>
                    }
                    onAbort={handleAbort}
                // onHelp={handleHelp}
                />
                {$service === '' ? (
                    <React.Fragment>
                        <Grid container spacing={2}>
                            <Grid item xs={12} align="center">
                                <ToggleButtonGroup
                                    className={classes.buttonGroup}
                                    size={breakpointUpSm ? 'medium' : 'small'}
                                    value={$filter}
                                    exclusive
                                    onChange={handleFilterChange}
                                >
                                </ToggleButtonGroup>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} className={classes.gridContainer}>
                            {serviceList}
                            <Grid item xs={12} className={classes.buttonAbort}>
                                <Button variant="outlined" color="default" onClick={handleAbort}>
                                    <Trans>Close</Trans>
                                </Button>
                            </Grid>
                        </Grid>
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                       {/* Horizontal Menu Card */}
                        <Card className={classes.panel} style={{ margin: '0px 0' }}> {/* Tambahkan style margin */}
                            <CardContent style={{ padding: '0px' }}> {/* Tambahkan style padding */}
                                <Tabs
                                    value={$tab}
                                    onChange={handleChangeTab}
                                    aria-label="edit navigation tabs"
                                    variant="fullWidth"
                                    indicatorColor="primary"
                                    textColor="primary"
                                    style={{ margin: '0' }} // Tambahkan style margin
                                    classes={{
                                        flexContainer: classes.horizontalTabs,
                                    }}
                                >
                                    <Tab
                                        label={<span className={classes.tabLabel}>General</span>}
                                        value="general"
                                        className={classes.horizontalTab}
                                    />
                                     <Tab
                                        label={<span className={classes.tabLabel}>Source & Encoding</span>}
                                        value="encoding"
                                        className={classes.horizontalTab}
                                    />
                                    <Tab
                                        label={<span className={classes.tabLabel}>Process control</span>}
                                        value="process"
                                        className={classes.horizontalTab}
                                    />
                                </Tabs>
                            </CardContent>
                        </Card>

                        <Grid container spacing={1}>
                            <Grid item xs={12}>
                                <TabPanel value={$tab} index="general" className={classes.panel}>
                                    <TabContent service={service}>
                                        <Grid item xs={12} sx={{ margin: '1em 0em 1em 0em' }}>
                                            <Typography>{service.description}</Typography>
                                        </Grid>
                                        <Grid item xs={12}>
                                            <TextField
                                                variant="outlined"
                                                fullWidth
                                                label={<Trans>Service name</Trans>}
                                                value={$settings.name}
                                                onChange={handleServiceName}
                                            />
                                        </Grid>
                                        <Grid item xs={12}>
                                            <ServiceControl
                                                settings={$settings.settings}
                                                skills={serviceSkills}
                                                metadata={$metadata}
                                                streams={$settings.streams}
                                                onChange={handleServiceChange}
                                                upstreamid={props.upstreamid}
                                            />
                                        </Grid>
                                    </TabContent>
                                </TabPanel>
                                <TabPanel value={$tab} index="process" className={classes.panel}>
                                    <TabContent service={service}>
                                        <Grid item xs={12}>
                                            <Typography variant="h2">
                                                <Trans>Process</Trans>
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={12}>
                                            <ProcessControl settings={$settings.control.process} onChange={handleControlChange('process')} />
                                        </Grid>

                                    </TabContent>
                                </TabPanel>
                                <TabPanel value={$tab} index="encoding" className={classes.panel}>
                                    <TabContent service={service}>
                                        <Grid item xs={12}>
                                            <Typography variant="h2">
                                                <Trans>Source & Encoding</Trans>
                                            </Typography>
                                        </Grid>

                                        <Grid item xs={12}>
                                            <Typography variant="h4">
                                                <Trans>Video</Trans>
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={12}>
                                            <EncodingSelect
                                                type="video"
                                                streams={$sources[0].streams}
                                                profile={$settings.profiles[0].video}
                                                codecs={serviceSkills.codecs.video}
                                                skills={$skills}
                                                onChange={handleProcessing('video')}
                                            />
                                        </Grid>
                                        {$settings.profiles[0].video.encoder.coder !== 'copy' && (
                                            <Grid item xs={12}>
                                                <FilterSelect
                                                    type="video"
                                                    profile={$settings.profiles[0].video}
                                                    availableFilters={$skills.filter}
                                                    onChange={handleProcessingFilter('video')}
                                                />
                                            </Grid>
                                        )}
                                        <Grid item xs={12}>
                                            <Typography variant="h4">
                                                <Trans>Audio</Trans>
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={12}>
                                            <EncodingSelect
                                                type="audio"
                                                streams={$sources[0].streams}
                                                profile={$settings.profiles[0].audio}
                                                codecs={serviceSkills.codecs.audio}
                                                skills={$skills}
                                                onChange={handleProcessing('audio')}
                                            />
                                        </Grid>
                                        {$settings.profiles[0].audio.encoder.coder !== 'copy' && (
                                            <Grid item xs={12}>
                                                <FilterSelect
                                                    type="audio"
                                                    profile={$settings.profiles[0].audio}
                                                    availableFilters={$skills.filter}
                                                    onChange={handleProcessingFilter('audio')}
                                                />
                                            </Grid>
                                        )}
                                    </TabContent>
                                </TabPanel>
                            </Grid>
                        </Grid>
                        <PaperFooter
                            buttonsLeft={
                                <React.Fragment>
                                    <Button variant="outlined" color="default" onClick={handleAbort}>
                                        <Trans>Close</Trans>
                                    </Button>
                                    <Button variant="outlined" color="default" onClick={handleServiceSelect('')}>
                                        <Trans>Back</Trans>
                                    </Button>
                                </React.Fragment>
                            }
                            buttonsRight={
                                <Button variant="outlined" color="primary" onClick={handleServiceDone} disabled={$settings.output === null || $saving === true}>
                                    <Trans>Save</Trans>
                                </Button>
                            }
                        />
                    </React.Fragment>
                )}
            </Paper>
            <Backdrop open={$saving}>
                <CircularProgress color="inherit" />
            </Backdrop>
                          {/* Snackbar untuk notifikasi validasi */}
                          <Snackbar
                            open={openSnackbar}
                            autoHideDuration={3000}
                            onClose={() => setOpenSnackbar(false)}
                            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                        >
                            <Alert onClose={() => setOpenSnackbar(false)} severity="error" sx={{ width: '100%' }}>
                                {snackbarMessage}
                            </Alert>
                        </Snackbar>
        </React.Fragment>
    );
}

Add.defaultProps = {
    upstreamid: null,
};

Add.propTypes = {
    upstreamid: PropTypes.object.isRequired,
};