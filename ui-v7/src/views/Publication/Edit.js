import PropTypes from 'prop-types';
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Trans, t } from '@lingui/macro';
import { useLingui } from '@lingui/react';
import Backdrop from '@mui/material/Backdrop';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import makeStyles from '@mui/styles/makeStyles';

// Tambahkan import ini
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';

import NotifyContext from '../../contexts/Notify';
import useInterval from '../../hooks/useInterval';
import BoxText from '../../misc/BoxText';
import LimitsControl from '../../misc/controls/Limits';
import ProcessControl from '../../misc/controls/Process';
import SourceControl from '../../misc/controls/Source';
import EncodingSelect from '../../misc/EncodingSelect';
import FilterSelect from '../../misc/FilterSelect';
import DebugModal from '../../misc/modals/Debug';
import Dialog from '../../misc/modals/Dialog';
import ProcessModal from '../../misc/modals/Process';
import Paper from '../../misc/Paper';
import PaperFooter from '../../misc/PaperFooter';
import PaperHeader from '../../misc/PaperHeader';
import TabPanel from '../../misc/TabPanel';
import TabsVerticalGrid from '../../misc/TabsVerticalGrid';
import H from '../../utils/help';
import * as M from '../../utils/metadata';
import * as helper from './helper';
import Process from './Process';
import Services from './Services';
import TabContent from './TabContent';



const useStyles = makeStyles((theme) => ({
    gridContainer: {
        marginTop: '0.5em',
        marginBottom: '1em',
    },
    link: {
        marginLeft: 10,
        wordWrap: 'anywhere',
    },
     horizontalMenu: {
        marginBottom: 0,
        marginTop: 0,
    },
    horizontalTabs: {
        display: 'flex',
        justifyContent: 'center',
    },
    horizontalTab: {
        padding: theme.spacing(1, 1), // Kurangi padding vertikal
        margin: theme.spacing(0, 1),
        minWidth: 'auto',
        marginTop: 0,
        fontWeight: 700,
        fontSize: '0.9rem',
        textTransform: 'none',
        border: `3px solid ${theme.palette.divider}`, // Added border
        borderRadius: theme.shape.borderRadius, // Added border radius
        color: theme.palette.text.secondary, // Default text color
        '&.Mui-selected': {
            color: theme.palette.primary.main, // Selected text color
            backgroundColor: theme.palette.action.selected, // Selected background color
        },
    },
    tabLabel: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
    },
    panel: {
        marginTop: theme.spacing(1), // Added margin top to the panel
    },
}));

export default function Edit(props) {
    const classes = useStyles();
    const { i18n } = useLingui();
    const { channelid: _channelid, service: _service, index: _index } = useParams();
    const id = props.upstreamid.GetEgressId(_service, _index);

    const navigate = useNavigate();
    const notify = React.useContext(NotifyContext);
    const [$ready, setReady] = React.useState(false);
    const [$settings, setSettings] = React.useState(M.getDefaultEgressMetadata());
    const [$sources, setSources] = React.useState([]);
    const [$localSources, setLocalSources] = React.useState([]);
    const [$tab, setTab] = React.useState('general');
    const [$progress, setProgress] = React.useState({});
    const [$processDetails, setProcessDetails] = React.useState({
        open: false,
        data: {
            prelude: [],
            log: [],
        },
    });
    const processLogTimer = React.useRef();
    const [$processDebug, setProcessDebug] = React.useState({
        open: false,
        data: '',
    });
    const [$unsavedChanges, setUnsavedChanges] = React.useState(false);
    const [$skills, setSkills] = React.useState(null);
    const [$metadata, setMetadata] = React.useState({
        name: '',
        description: '',
        license: '',
    });
    const [$deleteDialog, setDeleteDialog] = React.useState(false);
    const [$saving, setSaving] = React.useState(false);
    const [$service, setService] = React.useState(null);
    const [$serviceSkills, setServiceSkills] = React.useState(null);
    const [$invalid, setInvalid] = React.useState('');

    // State untuk validasi dan notifikasi
    const [openSnackbar, setOpenSnackbar] = React.useState(false);
    const [snackbarMessage, setSnackbarMessage] = React.useState('');

    useInterval(async () => {
        await update(false);
    }, 1000);

    React.useEffect(() => {
        (async () => {
            if (!_channelid) {
                // Channel ID tidak valid, mungkin setelah delete
                return; // Atau redirect ke halaman error
            }
            await update(true);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [_channelid, props.upstreamid]); // Dependensi: _channelid dan props.upstreamid

    React.useEffect(() => {
        if ($invalid.length !== 0) {
            navigate($invalid, { replace: true });
        }
    }, [navigate, $invalid]);

    const update = async (isFirst) => {
        if (!_channelid) {
            return; // Stop jika tidak ada channel ID
        }

        const channelid = props.upstreamid.SelectChannel(_channelid);
        if (channelid === '' || channelid !== _channelid) {
            setInvalid('/');
            return;
        }

        const proc = await props.upstreamid.GetEgress(_channelid, id, ['state']);
        if (proc === null) {
            notify.Dispatch('warning', 'notfound:egress:' + _service, `Publikasi Live tidak ada!`);
            setInvalid(`/${_channelid}`);
            return;
        }

        setProgress(proc.progress);

        if (isFirst === true) {
            const s = Services.Get(_service);
            if (s === null) {
                notify.Dispatch('warning', 'notfound:egress:' + _service, `Publikasi Live tidak ada!`);
                setInvalid(`/${_channelid}/`);
                return null;
            }

            setService(s);

            const skills = await props.upstreamid.Skills();
            setSkills(skills);

            const serviceSkills = helper.conflateServiceSkills(s.requires, skills);
            setServiceSkills(serviceSkills);

            const ingest = await props.upstreamid.GetIngestMetadata(_channelid);
            console.log("inget=", ingest);

            setMetadata({
                ...$metadata,
                name: ingest.meta.name,
                description: ingest.meta.description,
                license: ingest.license,
            });

            const localSources = [];

            localSources.push('hls+' + ingest.control.hls.storage);

            if (ingest.control.rtmp.enable) {
                localSources.push('rtmp');
            }

            if (ingest.control.srt.enable) {
                localSources.push('srt');
            }

            setLocalSources(localSources);

            const sources = helper.createSourcesFromStreams(ingest.streams);

            setSources(sources);

            const settings = await props.upstreamid.GetEgressMetadata(_channelid, id);

            console.log("hasilnya", settings);

            const profiles = settings.profiles;
            profiles[0].video = helper.preselectProfile(profiles[0].video, 'video', ingest.streams, serviceSkills.codecs.video, skills);
            profiles[0].audio = helper.preselectProfile(profiles[0].audio, 'audio', ingest.streams, serviceSkills.codecs.audio, skills);

            settings.profiles = profiles;
            settings.streams = M.createOutputStreams(sources, profiles, false);

            setSettings(settings);

            setReady(true);
        }
    };

    const handleServiceAction = async (action) => {
        let state = 'disconnected';

        if (action === 'connect') {
            await props.upstreamid.StartEgress(_channelid, id);
            state = 'connecting';
        } else if (action === 'disconnect') {
            await props.upstreamid.StopEgress(_channelid, id);
            state = 'disconnecting';
        } else if (action === 'reconnect') {
            await props.upstreamid.StopEgress(_channelid, id);
            await props.upstreamid.StartEgress(_channelid, id);
            state = 'connecting';
        }

        setProgress({
            ...$progress,
            state: state,
        });
    };

    const handleServiceChange = (outputs, settings) => {
        if (!Array.isArray(outputs)) {
            outputs = [outputs];
        }

        setSettings({
            ...$settings,
            outputs: outputs,
            settings: settings,
        });

        setUnsavedChanges(true);
    };

    const handleEncoding = (type) => (encoder, decoder, automatic) => {
        const profiles = $settings.profiles;

        profiles[0][type].encoder = encoder;
        profiles[0][type].decoder = decoder;

        const streams = M.createOutputStreams($sources, profiles, false);

        let outputs = $settings.outputs;

        if ('createOutputs' in $service) {
            outputs = $service.createOutputs($settings.settings, $serviceSkills, $metadata, streams);
        }

        setSettings({
            ...$settings,
            profiles: profiles,
            streams: streams,
            outputs: outputs,
        });

        if (!automatic) {
            setUnsavedChanges(true);
        }
    };

    const handleFilter = (type) => (filter, automatic) => {
        const profiles = $settings.profiles;

        profiles[0][type].filter = filter;

        setSettings({
            ...$settings,
            profiles: profiles,
        });

        if (!automatic) {
            setUnsavedChanges(true);
        }
    };

    const handleServiceDone = async () => {
        setSaving(true);

        // Validasi form
        const isValid = validateForm();

        if (!isValid) {
            setSaving(false);
            return;
        }

        const [global, inputs, outputs] = helper.createInputsOutputs($sources, $settings.profiles, $settings.outputs, false);
        if (inputs.length === 0 || outputs.length === 0) {
            setSaving(false);
            notify.Dispatch('error', 'save:egress:' + $service, i18n._(t`The input profile is not complete. Please define a video and/or audio source.`));
            return;
        }

        // Gunakan UpdateEgress untuk menyimpan konfigurasi egress dasar
        const [, err] = await props.upstreamid.UpdateEgress(_channelid, id, global, inputs, outputs, $settings.control);
        if (err !== null) {
            setSaving(false);
            notify.Dispatch('error', 'save:egress:' + $service, i18n._(t`Failed to update publication service (${err.message})`));
            return;
        }

        // SIMPAN metadata menggunakan SetEgressMetadata
        await props.upstreamid.SetEgressMetadata(_channelid, id, $settings);

         // Kirim data streaming ke backend hanya jika service adalah YouTube
         if (_service === 'youtube') {
            const formatDateForISO8601 = (dateStr) => {
                if (!dateStr) return ""; // Return empty string if date is null/undefined
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) {
                    console.error("🚨 ERROR: Invalid date format!", dateStr);
                    return ""; // Return empty string if invalid date
                }
                return date.toISOString(); // ISO 8601 format
            };

            // console.log("🔍 Debugging $settings sebelum dikirim:", $settings);

            const rawId = id; // ID dari CreateEgress
            const youtubeChannelId = rawId.split(":").pop(); // Ambil UUID
            const channel = $settings.settings ? $settings.settings.channel : ""; //adalah channelid dari youtube

            const streamData = {
                channel_id: channel || "", //adalah channelid dari youtube, untuk menyimpan di data
                service: $service.id || "",
                youtube_channel_id: youtubeChannelId, // Hanya UUID
                stream_key: $settings.settings?.stream_key || "",
                stream_name: $settings.settings?.stream_name || "",
                schedule_enabled: !!$settings.settings?.schedule_enabled, // Pastikan field ini dikirim
                start_time: $settings.settings?.schedule_enabled
                    ? formatDateForISO8601($settings.settings?.start_time)
                    : "",
                end_time: $settings.settings?.schedule_enabled
                    ? formatDateForISO8601($settings.settings?.end_time)
                    : "",
                title: $settings.settings?.title || "",
                description: $settings.settings?.description || "",
                youtube_api: !!$settings.settings?.youtube_api, // Pastikan boolean
                aicontent: !!$settings.settings?.aicontent,
                chatbot: !!$settings.settings?.chatbot,
                thumbnail: $settings.settings?.thumbnail || "",
                privacy: $settings.settings?.privacy || "",
                order: $settings.settings?.order || "listed",
                mode: $settings.settings?.mode || "default",
                chatbot_keyword: $settings.settings?.chatbot_keyword || "",
                schedule_repeat: $settings.settings?.schedule_repeat || 0, // Sertakan schedule_repeat
                status_thumbnails: $settings.settings?.status_thumbnails || "",
                repeatSchedule: $settings.settings?.repeatSchedule || 'off',
                autoGenerateTitle: !!$settings.settings?.autoGenerateTitle,
                manualTitleRotating: !!$settings.settings?.manualTitleRotating,
                manualTitles: $settings.settings?.manualTitles || '',
                currentTitleIndex: $settings.settings?.currentTitleIndex || 0, // Index untuk title rotation
                customScheduleDates: $settings.settings?.customScheduleDates || [], // Tambahan untuk custom calendar dates
            };

            streamData.youtube_channel_id = youtubeChannelId;

            // console.log("Data sebelum fetch:", {
            //     channel_id: streamData.channel_id,
            //     youtube_channel_id: streamData.youtube_channel_id
            // });

            // console.log("Mengirim data ke backend:", streamData); // Debugging

            if ($settings.settings?.youtube_api === true) {
                // console.log("▶️ idyoutubechannel:", channel)
                // console.log("▶️ idappchannel:", youtubeChannelId)

                // console.log("Mengirim data ke backend:", streamData);
                try {
                    // PERUBAHAN PENTING: Gunakan UpstreamId.UpdateYoutubeLivestream
                    const data = await props.upstreamid.UpdateYoutubeLivestream(youtubeChannelId, streamData);
                    // console.log("✅ Data berhasil diperbarui:", data);

                } catch (error) {
                    // console.error("🚨 Error saat mengirim data ke backend:", error);
                    notify.Dispatch('error', 'save:egress:' + $service, `Gagal memperbarui publikasi live (${error.message})`);                    setSaving(false); // Set saving ke false agar tombol aktif kembali
                    return;
                }
            }
        }

        let message = 'Publikasi Live berhasil diperbarui';
        if ($settings.name.length !== 0) {
            message = `Publikasi Live "${$settings.name}" berhasil diperbarui`;
        }

        setSaving(false);
        notify.Dispatch('success', 'save:egress:' + $service, message);
        navigate(`/${_channelid}/`);
    };

    const handleServiceName = (event) => {
        const name = event.target.value;

        setSettings({
            ...$settings,
            name: name,
        });

        setUnsavedChanges(true);
    };

    const handleControlChange = (what) => (control, automatic) => {
        setSettings({
            ...$settings,
            control: {
                ...$settings.control,
                [what]: control,
            },
        });

        if (automatic === false) {
            setUnsavedChanges(true);
        }
    };

    const handleServiceDeleteDialog = () => {
        setDeleteDialog(!$deleteDialog);
    };

    const handleServiceDelete = async () => {
        setSaving(true);

        // Ambil youtubeChannelId dari id
        const youtubeChannelId = id.split(":").pop();

        try {
            // HAPUS data YouTube hanya jika service adalah YouTube
            if (_service === "youtube") {
                await props.upstreamid.DeleteYoutubeLivestream(youtubeChannelId);
            } else {
            }

            // HAPUS Egress (tetap dilakukan terlepas dari service)
            const res = await props.upstreamid.DeleteEgress(_channelid, id);
            if (res === false) {
                setSaving(false);
                notify.Dispatch('warning', 'delete:egress:' + _service, `Publikasi Live "${$settings.name}" tidak bisa dihapus`);
                return;
            }

            setSaving(false);
            notify.Dispatch('success', 'delete:egress:' + _service, `Publikasi Live "${$settings.name}" berhasil dihapus`);
            navigate(`/${_channelid}`);

        } catch (error) {
            console.error("🚨 Error saat menghapus layanan publikasi:", error);
            setSaving(false);
            notify.Dispatch('error', 'delete:egress:' + _service, `Publikasi Live "${$settings.name}" gagal dihapus: ${error.message}`);
        }
    };

    const handleAbort = () => {
        navigate(`/${_channelid}/`);
    };

    const handleChangeTab = (event, value) => {
        setTab(value);
    };

    const handleHelp = (topic) => () => {
        if (!topic) {
            H('publication-' + $tab);
            return;
        }

        H(topic);
    };

    const handleProcessDetails = async (event) => {
        event.preventDefault();

        const open = !$processDetails.open;
        let logdata = {
            prelude: [],
            log: [],
        };

        if (open === true) {
            const data = await props.upstreamid.GetEgressLog(_channelid, id);
            if (data !== null) {
                logdata = data;
            }

            processLogTimer.current = setInterval(async () => {
                await updateProcessDetailsLog();
            }, 1000);
        } else {
            clearInterval(processLogTimer.current);
        }

        setProcessDetails({
            ...$processDetails,
            open: open,
            data: logdata,
        });
    };

    const updateProcessDetailsLog = async () => {
        const data = await props.upstreamid.GetEgressLog(_channelid, id);
        if (data !== null) {
            setProcessDetails({
                ...$processDetails,
                open: true,
                data: data,
            });
        }
    };

    const handleProcessDebug = async (event) => {
        event.preventDefault();

        const show = !$processDebug.open;
        let data = '';

        if (show === true) {
            const debug = await props.upstreamid.GetEgressDebug(_channelid, id);
            data = JSON.stringify(debug, null, 2);
        }

        setProcessDebug({
            ...$processDebug,
            open: show,
            data: data,
        });
    };

   // Fungsi Validasi Form
   const validateForm = () => {
        // Validasi hanya untuk service YouTube
        if (_service === 'youtube') {
            const youtubeAPIEnabled = $settings.settings?.youtube_api;

            if (youtubeAPIEnabled) {
                if (!$settings.settings?.channel) {
                    setSnackbarMessage('Channel wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }

                if (!$settings.settings?.stream_key) {
                    setSnackbarMessage('Stream Key wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }

                if (!$settings.settings?.title) {
                    setSnackbarMessage('Title wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }
                return true;
            } else {
                if (!$settings.settings?.stream_key) {
                    setSnackbarMessage('Stream Key wajib diisi!');
                    setOpenSnackbar(true);
                    return false;
                }
                return true;
            }
        }
        // Lewati validasi jika bukan YouTube
        return true;
    };

    if ($ready === false) {
        return null;
    }

    const ServiceControl = $service.component;

    const title = $settings.name.length === 0 ? $service.name : $settings.name;

    return (
        <React.Fragment>
            <Paper xs={12} md={10}>
                <PaperHeader
                    title={
                        <React.Fragment>
                            <Trans>Edit: {title}</Trans>
                        </React.Fragment>
                    }
                    onAbort={handleAbort}

                />

              {/* Horizontal Menu Card */}
                <Card className={classes.panel} style={{ margin: '0px 0' }}> {/* Tambahkan style margin */}
                    <CardContent style={{ padding: '0px' }}> {/* Tambahkan style padding */}
                        <Tabs
                            value={$tab}
                            onChange={handleChangeTab}
                            aria-label="edit navigation tabs"
                            variant="fullWidth"
                            indicatorColor="primary"
                            textColor="primary"
                            style={{ margin: '0' }} // Tambahkan style margin
                            classes={{
                                flexContainer: classes.horizontalTabs,
                            }}
                        >
                            <Tab
                                label={<span className={classes.tabLabel}>General</span>}
                                value="general"
                                className={classes.horizontalTab}
                            />
                             <Tab
                                label={<span className={classes.tabLabel}>Source & Encoding</span>}
                                value="encoding"
                                className={classes.horizontalTab}
                            />
                            <Tab
                                label={<span className={classes.tabLabel}>Process control</span>}
                                value="process"
                                className={classes.horizontalTab}
                            />
                        </Tabs>
                    </CardContent>
                </Card>

                <Grid container spacing={1}>
                  <Grid item xs={12}>
                     <TabPanel value={$tab} index="general" className={classes.panel}>
                        <TabContent service={$service}>
                            <Grid item xs={12} sx={{ margin: '1em 0em 1em 0em' }}>
                                <Typography>{$service.description}</Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    variant="outlined"
                                    fullWidth
                                    label={<Trans>Service name</Trans>}
                                    value={$settings.name}
                                    onChange={handleServiceName}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <ServiceControl
                                    settings={$settings.settings}
                                    skills={$serviceSkills}
                                    metadata={$metadata}
                                    streams={$settings.streams}
                                    onChange={handleServiceChange}
                                    upstreamid={props.upstreamid}
                                />
                            </Grid>
                        </TabContent>
                    </TabPanel>
                    <TabPanel value={$tab} index="process" className={classes.panel}>
                        <TabContent service={$service}>
                            <Grid item xs={12}>
                                <Typography variant="h2">
                                    <Trans>Process</Trans>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <ProcessControl settings={$settings.control.process} onChange={handleControlChange('process')} />
                            </Grid>
                            <Grid item xs={12}>
                                <Divider />
                            </Grid>
                            <Grid item xs={12}>
                                <Typography variant="h2">
                                    <Trans>Limits</Trans>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <LimitsControl settings={$settings.control.limits} onChange={handleControlChange('limits')} />
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={1} className={classes.gridContainer}>
                                    {$unsavedChanges === true && (
                                        <Grid item xs={12}>
                                            <BoxText>
                                                <Typography variant="body2" gutterBottom>
                                                    <Trans>You have unsaved changes. Please save them before you can control the service again.</Trans>
                                                </Typography>
                                            </BoxText>
                                        </Grid>
                                    )}
                                </Grid>
                            </Grid>
                        </TabContent>
                    </TabPanel>
                    <TabPanel value={$tab} index="encoding" className={classes.panel}>
                        <TabContent service={$service}>
                            <Grid item xs={12}>
                                <Typography variant="h2">
                                    <Trans>Source & Encoding</Trans>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <Typography variant="h4">
                                    <Trans>Video settings</Trans>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <EncodingSelect
                                    type="video"
                                    streams={$sources[0].streams}
                                    profile={$settings.profiles[0].video}
                                    codecs={$serviceSkills.codecs.video}
                                    skills={$skills}
                                    onChange={handleEncoding('video')}
                                />
                            </Grid>
                            {$settings.profiles[0].video.encoder.coder !== 'copy' && (
                                <Grid item xs={12}>
                                    <FilterSelect
                                        type="video"
                                        profile={$settings.profiles[0].video}
                                        availableFilters={$skills.filter}
                                        onChange={handleFilter('video')}
                                    />
                                </Grid>
                            )}
                            <Grid item xs={12}>
                                <Typography variant="h4">
                                    <Trans>Audio settings</Trans>
                                </Typography>
                            </Grid>
                            <Grid item xs={12}>
                                <EncodingSelect
                                    type="audio"
                                    streams={$sources[0].streams}
                                    profile={$settings.profiles[0].audio}
                                    codecs={$serviceSkills.codecs.audio}
                                    skills={$skills}
                                    onChange={handleEncoding('audio')}
                                />
                            </Grid>
                            {$settings.profiles[0].audio.encoder.coder !== 'copy' && (
                                <Grid item xs={12}>
                                    <FilterSelect
                                        type="audio"
                                        profile={$settings.profiles[0].audio}
                                        availableFilters={$skills.filter}
                                        onChange={handleFilter('audio')}
                                    />
                                </Grid>
                            )}
                        </TabContent>
                    </TabPanel>
                   </Grid>
                </Grid>
                <PaperFooter
                    buttonsLeft={
                        <Button variant="outlined" color="default" onClick={handleAbort}>
                            <Trans>Close</Trans>
                        </Button>
                    }
                    buttonsRight={
                        <React.Fragment>
                            <Button variant="outlined" color="primary" disabled={$unsavedChanges === false || $saving === true} onClick={handleServiceDone}>
                                <Trans>Save</Trans>
                            </Button>
                            <Button variant="outlined" color="secondary" disabled={$saving === true} onClick={handleServiceDeleteDialog}>
                                <Trans>Delete</Trans>
                            </Button>
                        </React.Fragment>
                    }
                />
            </Paper>

            <Dialog
                open={$deleteDialog}
                onClose={handleServiceDeleteDialog}
                title={`Apakah Anda ingin menghapus ${title}?`}
                buttonsLeft={
                    <Button variant="outlined" color="default" onClick={handleServiceDeleteDialog}>
                        <Trans>Abort</Trans>
                    </Button>
                }
                buttonsRight={
                    <Button variant="outlined" color="secondary" onClick={handleServiceDelete}>
                        <Trans>Delete</Trans>
                    </Button>
                }
            >
                <Typography>
                    Settingan live akan di hapus dan tidak dapat dibatalkan. Jika livestreaming aktif, maka akan dihentikan.
                </Typography>
            </Dialog>
            <Backdrop open={$saving}>
                <CircularProgress color="inherit" />
            </Backdrop>

            {/* Snackbar untuk notifikasi validasi */}
            <Snackbar
                open={openSnackbar}
                autoHideDuration={3000}
                onClose={() => setOpenSnackbar(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert onClose={() => setOpenSnackbar(false)} severity="error" sx={{ width: '100%' }}>
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </React.Fragment>
    );
}

Edit.defaultProps = {
    upstreamid: null,
};

Edit.propTypes = {
    upstreamid: PropTypes.object.isRequired,
};