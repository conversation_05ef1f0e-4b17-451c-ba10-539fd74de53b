import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLingui } from '@lingui/react';
import NotifyContext from '../../contexts/Notify';

import { Trans, t } from '@lingui/macro';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import PersonIcon from '@mui/icons-material/Person';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import makeStyles from '@mui/styles/makeStyles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';

import useInterval from '../../hooks/useInterval';
import Number from '../../misc/Number';
import PaperHeader from '../../misc/PaperHeader';
import { formatFriendlyDate, isNowBetween } from '../../utils/custom';
import H from '../../utils/help';
import Services from '../Publication/Services';
import Egress from './Egress';
import * as M from '../../utils/metadata';
import * as helper from './../Publication/helper.js';
import moment from 'moment-timezone';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import Box from '@mui/material/Box';
import { green, grey } from '@mui/material/colors';
import SyncIcon from '@mui/icons-material/Sync';
import DoneIcon from '@mui/icons-material/Done';

const useStyles = makeStyles((theme) => ({
    viewerCount: {
        fontSize: '3.5rem',
        fontWeight: 600,
    },
    vierwerDescription: {
        marginTop: '-1em',
    },
    vierwerTypo: {
        fontSize: '1.1rem',
    },
    bandwidth: {
        marginBottom: '.3em',
    },
    bandwidthCount: {
        fontSize: '2.5rem',
        fontWeight: 600,
    },
    bandwidthDescription: {
        marginTop: '-.5em',
    },
    bandwidthIcon: {
        fontSize: '1.7rem',
        paddingRight: 7,
    },
    schedule: {
        fontSize: '0.9rem',
        color: theme.palette.text.secondary,
    },
    scheduleStatus: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing(0.5),
        fontSize: '0.8rem',
        fontStyle: 'italic',
        color: theme.palette.text.secondary,
    },
    tokenAlert: {
        marginTop: theme.spacing(1),
    },
    tokenItem: {
        marginRight: theme.spacing(2),
    },
    lowToken: {
        color: 'red',
    },
    tokenContainer: {
        padding: theme.spacing(1),
        border: '1px solid #beffa6',
        marginBottom: theme.spacing(1),
    },
    lowTokenBorder: {
        borderColor: '#f44336',
    },
    scheduleInfo: {
        marginTop: theme.spacing(0.5),
        padding: theme.spacing(0.25, 0.5),
        fontSize: '0.6rem',
    },
}));

// ### TAMBAHAN 1: Buat fungsi helper untuk teks jadwal ###
const getRepeatScheduleText = (schedule, customDates = []) => {
    switch (schedule) {
        case 'daily':
            return 'Setiap Hari';
        case 'weekly':
            return 'Setiap Minggu';
        case 'monthly':
            return 'Setiap Bulan';
        case 'yearly':
            return 'Setiap Tahun';
        case 'custom':
            if (customDates && customDates.length > 0) {
                return `Tanggal Custom (${customDates.length} hari)`;
            }
            return 'Tanggal Custom';
        default:
            return null; // Jangan tampilkan apa-apa jika 'off' atau tidak dikenal
    }
};

// Fungsi untuk format tanggal custom yang lebih readable
const formatCustomDates = (customDates) => {
    if (!customDates || customDates.length === 0) {
        return 'Tidak ada tanggal dipilih';
    }

    // Sort dates
    const sortedDates = [...customDates].sort();

    // Format tanggal ke format yang lebih readable
    const formattedDates = sortedDates.map(dateStr => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('id-ID', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    });

    // Jika terlalu banyak tanggal, tampilkan beberapa saja
    if (formattedDates.length > 5) {
        return `${formattedDates.slice(0, 3).join(', ')}, ... (+${formattedDates.length - 3} lainnya)`;
    }

    return formattedDates.join(', ');
};

export default function Publication({ channelid = '', upstreamid = null }) {
    const [$settings, setSettings] = React.useState(M.getDefaultEgressMetadata());
    const [$sources, setSources] = React.useState([]);

    const classes = useStyles();
    const { i18n } = useLingui();

    const notify = React.useContext(NotifyContext);

    const navigate = useNavigate();
    const services = Services.IDs();
    const [$serviceSkills, setServiceSkills] = React.useState(null);
    const [$skills, setSkills] = React.useState(null);

    const [$p, setP] = React.useState({
        name: null,
        channelid: null,
        id: "",
        services: null
    });
    const [$localSources, setLocalSources] = React.useState([]);

    const [$metadata, setMetadata] = React.useState({
        name: '',
        description: '',
        license: '',
    });

    const [$egresses, setEgresses] = React.useState([]);
    const [$session, setSession] = React.useState({
        bandwidth: 0,
    });

    const [clientConfig, setClientConfig] = React.useState({
        TokenAPI: 0,
        TokenChatboot: 0,
    });

    const [youtubeAPIEnabled, setYoutubeAPIEnabled] = React.useState(null);
    const [youtubeChatbotEnabled, setYoutubeChatbotEnabled] = React.useState(null);

    const [tokenAlert, setTokenAlert] = React.useState(false);

    useInterval(async () => {
        await update();
    }, 1000);

    useInterval(async () => {
        await sessions();
    }, 1000);

    React.useEffect(() => {
        (async () => {
            await update();
        })();
    }, []);

    React.useEffect(() => {
        (async () => {
            if ($p !== null) {
                await handleDelete();
            }
        })();
    }, [$settings, $p]);

    React.useEffect(() => {
        const fetchClientConfig = async () => {
            if (upstreamid && upstreamid.clientConfig) {
                const { TokenAPI, TokenChatboot, YoutubeAPI, YoutubeChatbot } = upstreamid.clientConfig;

                setClientConfig({
                    TokenAPI: TokenAPI || 0,
                    TokenChatboot: TokenChatboot || 0,
                });

                setYoutubeAPIEnabled(YoutubeAPI || false);
                setYoutubeChatbotEnabled(YoutubeChatbot || false);
            }
        };

        fetchClientConfig();
    }, [upstreamid, upstreamid.clientConfig]);

    React.useEffect(() => {
        if (youtubeAPIEnabled !== null && youtubeChatbotEnabled !== null) {
            if (youtubeAPIEnabled && clientConfig.TokenAPI < 10) {
                setTokenAlert(true);
            } else if (youtubeChatbotEnabled && clientConfig.TokenChatboot < 10) {
                setTokenAlert(true);
            } else {
                setTokenAlert(false);
            }
        }
    }, [clientConfig.TokenAPI, clientConfig.TokenChatboot, youtubeAPIEnabled, youtubeChatbotEnabled]);

    const update = async () => {
        const egresses = [];
        const processes = await upstreamid.ListIngestEgresses(channelid, services);

        for (let p of processes) {
            let processData = { ...p };

            try {
                const metadata = await upstreamid.GetEgressMetadata(channelid, p.id);
                
                if (metadata && metadata.settings) {
                    processData.startTime = metadata.settings.start_time;
                    processData.endTime = metadata.settings.end_time;
                    processData.name = metadata.name || p.name;
                    processData.settings = metadata.settings;
                } else {
                    processData.startTime = p.start_time;
                    processData.endTime = p.end_time;
                }

            } catch (error) {
                console.warn(`Could not fetch metadata for egress ${p.id}:`, error);
                processData.startTime = p.start_time;
                processData.endTime = p.end_time;
            }

            processData["isStart"] = isNowBetween(processData.startTime, processData.endTime);
            processData["channelid"] = channelid;
            
            egresses.push(processData);
        }

        setEgresses(egresses);
    };

    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    const sessions = async () => {
        const current = await upstreamid.CurrentSessions(['ffmpeg', 'hls', 'rtmp', 'srt']);

        setSession({
            viewer: current.sessions,
            bandwidth: current.bitrate_kbit,
        });
    };

    const handleServiceAdd = (event) => {
        event.preventDefault();
        navigate(`/${channelid}/publication/`);
    };

    const buildDataDeelete = async (p) => {
        const s = Services.Get(p.service);
        const skills = await upstreamid.Skills();
        setSkills({ ...$skills, ...skills });
        const serviceSkills = helper.conflateServiceSkills(s.requires, skills);
        setServiceSkills({ ...$serviceSkills, ...serviceSkills });
        const ingest = await upstreamid.GetIngestMetadata(p.channelid);
        setMetadata({ ...$metadata, name: ingest.meta.name, description: ingest.meta.description, license: ingest.license });
        const localSources = [];
        localSources.push('hls+' + ingest.control.hls.storage);
        if (ingest.control.rtmp.enable) {
            localSources.push('rtmp');
        }
        setLocalSources({ $localSources, localSources });
        const sources = helper.createSourcesFromStreams(ingest.streams);
        setSources({ ...$sources, ...sources });
        const settings = await upstreamid.GetEgressMetadata(p.channelid, p.id);
        settings.outputs[0].start_time = "";
        settings.outputs[0].end_time = "";
        settings.settings.start_time = "";
        settings.settings.end_time = "";
        const profiles = settings.profiles;
        profiles[0].video = helper.preselectProfile(profiles[0].video, 'video', ingest.streams, serviceSkills.codecs.video, skills);
        profiles[0].audio = helper.preselectProfile(profiles[0].audio, 'audio', ingest.streams, serviceSkills.codecs.audio, skills);
        settings.profiles = profiles;
        settings.streams = M.createOutputStreams(sources, profiles, false);
        settings["eg"] = { channelid: p.channelid, id: p.id, services: p.service };
        setSettings({ ...$settings, ...settings });
    }

    const handleDelete = async () => {
        if ($settings.name) {
            const [global, inputs, outputs] = helper.createInputsOutputs($sources, $settings.profiles, $settings.outputs, false);
            const [, err] = await upstreamid.UpdateEgress($settings.eg.channelid, $settings.eg.id, global, inputs, outputs, $settings.control);
            if (err !== null) {
                notify.Dispatch('error', 'save:egress:' + $settings.eg.service, i18n._(t`Failed to store publication service (${err.message})`));
                return;
            }
            await upstreamid.SetEgressMetadata($settings.eg.channelid, $settings.eg.id, $settings);
            setSettings({ ...$settings, ...M.getDefaultEgressMetadata() });
        }
    }

    const handleServiceEdit = (service, index) => () => {
        let target = `/${channelid}/publication/${service}`;
        if (service !== 'player') {
            target = target + '/' + index;
        }
        navigate(target);
    };

    const handleOrderChange = (id) => async (order) => {
        let res = false;
        if (order === 'start') {
            res = await upstreamid.StartEgress(channelid, id);
        } else if (order === 'restart') {
            res = await upstreamid.StopEgress(channelid, id);
            if (res === true) {
                res = await upstreamid.StartEgress(channelid, id);
            }
        } else if (order === 'stop') {
            res = await upstreamid.StopEgress(channelid, id);
        }
        return res;
    };

    const getScheduleInfo = (startTime, endTime) => {
        if (!startTime || !endTime) { return { status: null }; }
        const nowWIB = moment.tz(new Date(), 'Asia/Jakarta');
        const startWIB = moment.tz(startTime, 'Asia/Jakarta');
        const endWIB = moment.tz(endTime, 'Asia/Jakarta');
        if (nowWIB.isAfter(endWIB)) {
            return { status: 'done' };
        } else if (nowWIB.isBefore(startWIB)) {
            return { status: 'scheduled' };
        } else {
            return { status: 'running' };
        }
    };

    let egressesView = [];

 for (let e of $egresses.values()) {
        const scheduleInfo = getScheduleInfo(e.startTime, e.endTime);
        const repeatText = e.settings ? getRepeatScheduleText(e.settings.repeatSchedule, e.settings.customScheduleDates) : null;
        const customDatesText = e.settings?.repeatSchedule === 'custom' && e.settings?.customScheduleDates
            ? formatCustomDates(e.settings.customScheduleDates)
            : null;

        let statusContent = null;
        switch (scheduleInfo.status) {
            case 'running':
                statusContent = ( <Box className={classes.scheduleStatus}> <SyncIcon style={{ color: grey[500] }} /> Jadwal Berjalan </Box> );
                break;
            case 'done':
                statusContent = ( <Box className={classes.scheduleStatus}> <DoneIcon style={{ color: green[500] }} /> Jadwal Selesai </Box> );
                break;
            case 'scheduled':
                statusContent = ( <Box className={classes.scheduleStatus}> <HourglassEmptyIcon color="warning" /> Jadwal Loading </Box> );
                break;
            default:
                statusContent = null;
        }

        egressesView.push(
            <React.Fragment key={e.id}>
                <Grid item xs={12}> <Divider /> </Grid>
                <Grid item xs={12}>
                    <Egress
                        // ... (Props untuk Egress tetap sama, tidak perlu diubah)
                        isBetween={e.startTime !== "" ? isNowBetween(e.startTime, e.endTime) : true}
                        service={e.service}
                        name={e.name}
                        state={e.progress.state}
                        order={e.progress.order}
                        startTime={e.startTime ? e.startTime : ""}
                        reconnect={e.progress.reconnect !== -1}
                        onEdit={handleServiceEdit(e.service, e.index)}
                        onOrder={handleOrderChange(e.id)}
                        onDelete={() => buildDataDeelete(e)}
                        youtubeAPIEnabled={e.settings?.youtube_api}
                    />

                    {/* === PERUBAHAN UTAMA: Tambahkan Kondisi di Sini === */}
                    {e.settings?.schedule_enabled && e.startTime && e.endTime && (
                        <Grid item xs={12} align="center">
                            <Divider />
                            <Box display="flex" flexDirection="column" alignItems="center">
                                <Box className={classes.schedule}> Jadwal On: {formatFriendlyDate(e.startTime)} </Box>
                                <Box className={classes.schedule}> Jadwal Off: {formatFriendlyDate(e.endTime)} </Box>
                                
                                {statusContent}
                                
                                {repeatText && (
                                    <Box className={classes.schedule} sx={{ mt: 0.5, fontWeight: 'bold' }}>
                                        Diulangi: {repeatText}
                                    </Box>
                                )}

                                {customDatesText && (
                                    <Box className={classes.schedule} sx={{ mt: 0.5, fontSize: '0.75rem', color: 'text.secondary' }}>
                                        📅 {customDatesText}
                                    </Box>
                                )}
                            </Box>
                        </Grid>
                    )}
                    {/* ==================================================== */}
                    
                </Grid>
            </React.Fragment>
        );
    }

    return (
        <React.Fragment>
            <Paper marginBottom="0">
                <PaperHeader title="Publikasi Live" onAdd={handleServiceAdd} />
                <Grid container spacing={1}>
                    <Grid item xs={12} align="center" className={classes.bandwidth}>
                        <Typography component="div" className={classes.bandwidthCount}>
                            <Number value={$session.bandwidth} />
                        </Typography>
                        <Grid container direction="row" justifyContent="center" alignItems="center" className={classes.bandwidthDescription}>
                            <CloudUploadIcon className={classes.bandwidthIcon} />
                            <Typography> <Trans>kbit/s</Trans> </Typography>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        {youtubeAPIEnabled && (
                            <Paper className={`${classes.tokenContainer} ${clientConfig.TokenAPI < 10 ? classes.lowTokenBorder : ''}`} elevation={0}>
                                <Typography> Token Youtube = {clientConfig.TokenAPI} {clientConfig.TokenAPI < 10 ? " > Token kurang!" : ""} </Typography>
                            </Paper>
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        {youtubeChatbotEnabled && (
                            <Paper className={`${classes.tokenContainer} ${clientConfig.TokenChatboot < 10 ? classes.lowTokenBorder : ''}`} elevation={0}>
                                <Typography> Token Chatbot = {clientConfig.TokenChatboot} {clientConfig.TokenChatboot < 10 ? " > Token kurang!" : ""} </Typography>
                            </Paper>
                        )}
                    </Grid>
                    {youtubeAPIEnabled !== null && youtubeChatbotEnabled !== null && (youtubeAPIEnabled || youtubeChatbotEnabled) && tokenAlert && (
                        <Grid item xs={12}>
                            <Alert severity="warning" className={classes.tokenAlert}> Minimal Token Adalah 10! </Alert>
                        </Grid>
                    )}
                    {egressesView}
                </Grid>
            </Paper>
        </React.Fragment>
    );
}