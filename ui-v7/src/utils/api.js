class API {
    constructor(address) {
        this.base = '/api';
        this.address = address;
        this.token = '';

        this.cache = new Map();
    }

    _debug(message) {
        //console.log(`[CoreAPI] ${message}`);
    }

    _error(message) {
        console.error(`[CoreAPI] Error: ${message}`);
    }

    async _GET(path, options) {
      const key = path + JSON.stringify(options);

      const data = this.cache.get(key);
      if (data !== undefined) {
          const millis = Date.now() - data.ts;

          if (millis < 950) {
              return JSON.parse(JSON.stringify(data.payload));
          }
      }

      const ts = Date.now();

      const res = await this._call('GET', path, options);

      this.cache.set(key, {
          payload: JSON.parse(JSON.stringify(res)),
          ts: ts,
      });

      return res;
    }

    async _HEAD(path, options) {
      return await this._call('HEAD', path, options);
    }

    async _POST(path, options) {
      return await this._call('POST', path, options);
    }

    async _PUT(path, options) {
      return await this._call('PUT', path, options);
    }

    async _DELETE(path, options) {
      return await this._call('DELETE', path, options);
    }

    async _PATCH(path, options) {
      return await this._call('PATCH', path, options);
    }

    async _call(method, path, options = {}) {
        options = {
            method: method.toUpperCase(),
            expect: 'any',
            headers: {},
            token: '',
            ...options,
        };

        path = this.base + path;
        if (path !== '/') {
            // remove slash at the end of the path
            if (path[path.length - 1] === '/') {
                path = path.substring(0, path.length - 1);
            }
        }

        let token = '';

        if (options.token.length !== 0) {
            token = options.token;
        } else {
            if (typeof this.token === 'function') {
                token = await this.token();
            } else {
                token = this.token;
            }
        }

        if (token.length !== 0) {
            options.headers.Authorization = 'Bearer ' + token;
        }

        this._debug(`calling ${options.method} ${this.address + path}`);

        const res = {
            err: null,
            val: null,
        };

        let response = null;
		let body = null; // deklarasikan di luar try catch agar bisa diakses di catch

        try {
            response = await fetch(this.address + path, options);

            if (!response.ok) {
                res.err = {
                    code: response.status,
                    message: response.statusText,
                };
            }

            const contentType = response.headers.get('Content-Type');
            const isJSON = contentType != null && contentType.indexOf('application/json') !== -1;

            try {
                if (isJSON) {
                    body = await response.json();
                } else {
                    body = await response.text();
                }
            } catch (parseError) {
                res.err = {
                    code: -2,
                    message: `Error parsing response body: ${parseError.message}, original status text: ${response.statusText}`,
                };
                this._error(res.err.message);
                return res;
            }

            if (res.err) {
                console.error("Full response body:", body);

                if ('code' in body && 'message' in body) {
                    res.err.message = body.message;
                } else {
                    res.err.message = body; // Gunakan body langsung
                }
                this._error(res.err.message);
                return res;
            }


            res.val = body;

            if (options.expect === 'json' && !isJSON) {
                res.val = null;
                res.err = {
                    code: -2,
                    message: `The response is not JSON as expected (${contentType})`,
                };
                this._error(res.err.message);
            }

            return res;


        } catch (err) {
            res.err = {
                code: -1,
                message: err.message,
            };
            this._error(res.err.message);
            return res;
        }
    }

    SetAddress(address) {
        this.address = address;
    }

    SetToken(token) {
        this.token = token;
        this.cache = new Map();
    }

    async Login(username, password) {
        return await this._POST('/login', {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password,
            }),
            expect: 'json',
        });
    }

    async LoginWithToken(token) {
        return await this._POST('/login', {
            headers: {
                'Content-Type': 'application/json',
            },
            expect: 'json',
            token: token,
        });
    }

    async RefreshToken(refresh_token) {
        return await this._GET('/login/refresh', {
            expect: 'json',
            token: refresh_token,
        });
    }

    async About() {
        return await this._GET('/', {
            expect: 'json',
        });
    }

    async Skills() {
        return await this._GET('/v3/skills', {
            expect: 'json',
        });
    }

    async SkillsReload() {
        return await this._GET('/v3/skills/reload', {
            expect: 'json',
        });
    }

    async Config(type) {
        return await this._GET('/v3/config', {
            expect: 'json',
        });
    }

    async ConfigSet(config) {
        return await this._PUT('/v3/config', {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
            expect: 'json',
        });
    }

    async ConfigReload(type) {
        return await this._GET('/v3/config/reload');
    }

    async Log() {
        return await this._GET('/v3/log', {
            expect: 'json',
        });
    }

    async ActiveSessions(collectors) {
        return await this._GET('/v3/session/active?collectors=' + encodeURIComponent(collectors.join(',')), {
            expect: 'json',
        });
    }

    async SetMetadata(key, data) {
        return await this._PUT('/v3/metadata/' + encodeURIComponent(key), {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
            expect: 'json',
        });
    }

    async GetMetadata(key) {
        return await this._GET('/v3/metadata/' + encodeURIComponent(key), {
            expect: 'json',
        });
    }

    async DataHasFile(path) {
        return await this._HEAD('/v3/fs/disk' + path);
    }

    async DataPutFile(path, data) {
        return await this._PUT('/v3/fs/disk' + path, {
            headers: {
                'Content-Type': 'application/data',
            },
            body: data,
        });
    }

    async DataGetFile(path) {
        return await this._GET('/v3/fs/disk' + path);
    }

    async DataListFiles(pathPattern) {
        return await this._GET('/v3/fs/disk?glob=' + encodeURIComponent(pathPattern), {
            expect: 'json',
        });
    }

    async DataDeleteFile(path) {
        return await this._DELETE('/v3/fs/disk' + path);
    }

    async MemFSListFiles(pathPattern) {
        return await this._GET('/v3/fs/mem?glob=' + encodeURIComponent(pathPattern), {
            expect: 'json',
        });
    }

    async MemFSHasFile(path) {
        return await this._HEAD('/v3/fs/mem' + path);
    }

    async MemFSDeleteFile(path) {
        return await this._DELETE('/v3/fs/mem' + path);
    }

    async MemFSLinkFile(path, linkto) {
        return await this._PATCH('/v3/fs/mem/' + path, {
            headers: {
                'Content-Type': 'application/data',
            },
            body: linkto,
        });
    }

    async Processes(reference = '', ids = [], filter = []) {
        let url = '/v3/process';
        let params = [];

        if (reference.length !== 0) {
            params.push('reference=' + encodeURIComponent(reference));
        }

        if (ids.length !== 0) {
            params.push('id=' + encodeURIComponent(ids.join(',')));
        }

        if (filter.length !== 0) {
            params.push('filter=' + encodeURIComponent(filter.join(',')));
        }

        if (params.length !== 0) {
            url = url + '?' + params.join('&');
        }

        return await this._GET(url, {
            expect: 'json',
        });
    }

    async Process(name, filter = []) {
        let url = '/v3/process/' + name;
        if (filter.length !== 0) {
            url = url + '?filter=' + encodeURIComponent(filter.join(','));
        }

        return await this._GET(url, {
            expect: 'json',
        });
    }

    async ProcessConfig(name) {
        return await this._GET('/v3/process/' + encodeURIComponent(name) + '/config', {
            expect: 'json',
        });
    }

    async ProcessState(name) {
        return await this._GET('/v3/process/' + encodeURIComponent(name) + '/state', {
            expect: 'json',
        });
    }

    async ProcessReport(name) {
        return await this._GET('/v3/process/' + encodeURIComponent(name) + '/report', {
            expect: 'json',
        });
    }

    async ProcessCommand(name, command) {
        return await this._PUT('/v3/process/' + encodeURIComponent(name) + '/command', {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: command,
            }),
            expect: 'json',
        });
    }

    async ProcessDelete(name) {
        return await this._DELETE('/v3/process/' + encodeURIComponent(name));
    }

    async ProcessUpdate(name, config) {
        return await this._PUT('/v3/process/' + encodeURIComponent(name), {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
        });
    }

    async ProcessSetMetadata(name, key, data) {
        return await this._PUT('/v3/process/' + encodeURIComponent(name) + '/metadata/' + encodeURIComponent(key), {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
            expect: 'json',
        });
    }

    async ProcessGetMetadata(name, key) {
        return await this._GET('/v3/process/' + encodeURIComponent(name) + '/metadata/' + encodeURIComponent(key), {
            expect: 'json',
        });
    }

    async ProcessAdd(config) {
        return await this._POST('/v3/process', {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
            expect: 'json',
        });
    }

    async ProcessProbe(name) {
        return await this._GET('/v3/process/' + encodeURIComponent(name) + '/probe', {
            expect: 'json',
        });
    }

    async Metrics(query) {
        return await this._POST('/v3/metrics', {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(query),
            expect: 'json',
        });
    }



    async RTMPChannels() {
        const res = await this._GET('/v3/rtmp', {
            expect: 'json',
        });

        if (res.err !== null) {
            return res;
        }

        res.val = res.val.map((f) => f.name);

        return res;
    }

    async SRTChannels() {
        const res = await this._GET('/v3/srt', {
            expect: 'json',
        });

        if (res.err !== null) {
            return res;
        }

        const val = res.val;
        res.val = [];

        for (let path in val.publisher) {
            res.val.push(path);
        }

        return res;
    }
    //    diskusage API
    //     async DiskUsage() {
    //       return await this._GET('/diskusage', {
    //         expect: 'json',
    //       });
    //     }

             async Files() {
                const res = await this._GET('/files', {
                    expect: 'json',
                });

                if (res.err !== null) {
                    console.error("Error fetching files:", res.err);
                    throw new Error(res.err.message);
                }

                return res.val; // Kembalikan daftar file dari respons
            }

            async Delete(fileId) {
                const res = await this._DELETE(`/delete/${encodeURIComponent(fileId)}`);

                if (res.err !== null) {
                    console.error("Error deleting file:", res.err);
                    throw new Error(res.err.message);
                }

                return res.val; // Kembalikan nilai dari respons (mungkin pesan sukses)
            }

        // Fungsi API untuk mengunggah file
        async UploadFile(formData) {
            return await this._POST('/uploadfile', { // Gunakan _POST untuk upload
            body: formData, // FormData tidak perlu di-stringify
            expect: 'json', // Tambahkan ini
            });
        }

        // Fungsi API untuk mengunggah dari Google Drive
async UploadFromGDrive(formData, options) {
    const res = await this._POST('/upload/gdrive', {
        body: formData,
        expect: 'json',
    });

    console.log("API Response:", res);

    if (res.err !== null && res.err !== undefined) {
        console.error("Error uploading gdrive file:", res.err);
        throw new Error(res.err.message || "An unknown error occurred");
    }

    return res.val;
}

async GetUploadProgress(uploadId) {
        const res = await this._GET(`/upload/progress/${uploadId}`, {
            expect: 'json',
        });
        return res;
    }

async GetGDriveToken(accountKey) {
    try {
        const res = await this._POST('/gdrive/token', {
            body: JSON.stringify({ accountKey }),
            expect: 'json',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        if (res.err !== null) {
            console.error("Error getting gdrive token:", res.err);
            throw new Error(res.err.message);
        }

        return res.val;
    } catch (error) {
        console.error("Gagal mendapatkan token:", error);
        throw error;
    }
}
    
    

        async GetGDrivePickerURL(accountKey) {
            const res = await this._GET(`/gdrive/pickerurl?account_key=${accountKey}`, {
               expect: 'json',
           });
           if (res.err !== null) {
               console.error("Error GetGDrivePickerURL file:", res.err);
               throw new Error(res.err.message);
           }
   
           return res.val; //Kembalikan .val dari respons
       }

        async DeleteGDrive(accountKey) {
            try {
                const res = await this._DELETE(`/gdrive/accounts/${accountKey}`);
        
                if (res.err !== null) {
                    console.error("Error deleting file:", res.err);
                    throw new Error(res.err.message);
                }
        
                return res.val; // Kembalikan nilai dari respons (mungkin pesan sukses)
            } catch (error) {
                console.error("Gagal menghapus akun:", error);
                throw error;  // Re-throw error untuk ditangani di upstreamid
            }
        }

        // async UploadFromGDrive(formData, options) {
        //     const res = await this._POST('/upload/gdrive', { // Gunakan _POST untuk upload
        //         body: formData, // FormData tidak perlu di-stringify
        //         expect: 'json', // Tambahkan ini
        //         onUploadProgress: options.onUploadProgress
        //     });
        //     if (res.err !== null) {
        //         console.error("Error uploading gdrive file:", res.err);
        //         throw new Error(res.err.message);
        //     }
    
        //     return res.val; //Kembalikan .val dari respons
        // }

        // Google Drive API
        async listGDriveAccounts() {
            try {
                const response = await this._GET("/gdrive/accounts");
                return response.val;
            } catch (error) {
                console.error("Failed to list Google Drive accounts:", error);
                throw error;
            }
        }

     

        // Google Drive Auth API
        async getGDriveAuthURL(redirectURL) {
            try {
                const response = await this._GET(`/gdrive/authurl?redirect_url=${encodeURIComponent(redirectURL)}`);
                return response.val;
            } catch (error) {
                console.error("Failed to get Google Drive auth URL:", error);
                throw error;
            }
        }



        async getUnassignedFiles() {
            const res = await this._GET('/uploads/metadata', { // Menggunakan _GET
                expect: 'json',
            });
    
            if (res.err !== null) {
                console.error("Error fetching unassigned files:", res.err);
                throw new Error(res.err.message);
            }
    
            return res.val; // Kembalikan daftar file dari respons
        }
    
        async selectFile(uploadId, channelId) {
            const res = await this._POST('/uploads/select', { // Menggunakan _POST
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ uploadId: uploadId, channelId: channelId }),
                expect: 'json',
            });
    
            if (res.err !== null) {
                console.error("Error selecting file:", res.err);
                // Melempar error agar bisa ditangani di komponen
                throw new Error(res.err.message);
            }
    
            return res.val; // Kembalikan nilai dari respons (mungkin data file)
        }
        
        // async getDashboardData() {
        //     const [val, err] = await this._GET('/dashboard');
        //     if (err !== null) {
        //         console.error("Error fetching dashboard data:", err);
        //         throw new Error(err.message);
        //     }
        //     return val;
        // }

        async getDashboardData() {
            const res = await this._GET('/dashboard', {
                expect: 'json',
            });

            if (res.err !== null) {
                console.error("Error fetching files:", res.err);
                throw new Error(res.err.message);
            }

            return res.val; // Kembalikan daftar file dari respons
        }

// Youtube Channels API
async GetYoutubeChannels() {
    const res = await this._GET('/youtube/channels', {
        expect: 'json',
    });
    
    if (res.err !== null) {
        console.error("Error fetching files:", res.err);
        throw new Error(res.err.message);
    }

    return res.val; 
}

async DeleteYoutubeChannel(channelId) {
    return await this._DELETE(`/youtube/channels/${encodeURIComponent(channelId)}`);
}

async UploadThumbnail(formData) {
    try {
        const res = await this._POST('/youtube/uploads', {
            body: formData,
            expect: 'json',
        });

        // Periksa apakah ada error di _POST
        if (res.err !== null) {
            console.error("Error uploading thumbnail:", res.err);
            throw new Error(res.err.message);
        }

        // Periksa apakah ada error dari backend (di dalam res.val)
        if (res.val && res.val.error) {
            console.error("Error uploading thumbnail (backend):", res.val.error);
            throw new Error(res.val.error);
        }

        // Periksa apakah thumbnail_url ada di res.val
        if (res.val && res.val.thumbnail_url) {
            // Kembalikan URL thumbnail
            return res.val; // Kembalikan *seluruh* objek res.val, bukan hanya URL
        } else {
            // Jika thumbnail_url tidak ada, tampilkan pesan error
            console.error("Error: Backend tidak mengembalikan thumbnail_url!", res);
            throw new Error("Backend tidak mengembalikan thumbnail_url!");
        }

    } catch (error) {
        console.error("Error uploading thumbnail:", error);
        throw error;
    }
}

// Endpoint untuk membuat stream key
async CreateYoutubeStreamKey(channelId) {
    const res = await this._POST(`/youtube/streamkeys/${encodeURIComponent(channelId)}/create`);

    if (res.err !== null) {
        console.error("Error creating YouTube stream key:", res.err);
        throw new Error(res.err.message);
    }

    return res.val;
}
// Endpoint untuk mendapatkan stream key
async GetYoutubeStreamKeys(channelId) {
    const res = await this._GET(`/youtube/streamkeys/${encodeURIComponent(channelId)}`);

    if (res.err !== null) {
        console.error("Error creating YouTube stream key:", res.err);
        throw new Error(res.err.message);
    }

    return res.val;
}


async RefreshYoutubeStreamKeys(channelId) {
    const res = await this._GET(`/youtube/channels/streamkeys/refresh/${encodeURIComponent(channelId)}`);

    if (res.err !== null) {
        console.error("Error creating YouTube stream key:", res.err);
        throw new Error(res.err.message);
    }

    return res.val;
}

async DeleteYoutubeStreamKey(streamId) {
    return await this._DELETE(`/youtube/streamkeys/${encodeURIComponent(streamId)}`);
}

 async CreateYoutubeLivestream(youtubeChannelId, streamData) {
        try {
            const res = await this._POST(`/youtube/livestreams/${youtubeChannelId}`, {
                body: JSON.stringify(streamData),
                headers: {
                    'Content-Type': 'application/json',
                },
                expect: 'json',
            });

            // Periksa apakah respons memiliki error
            if (res && res.err !== null) {
                console.error("Error creating YouTube livestream:", res.err);
                throw new Error(res.err.message);
            }

            return res.val; // Kembalikan data dari respons
        } catch (error) {
            console.error("Error creating YouTube livestream:", error);
            throw error;
        }
    }

    async UpdateYoutubeLivestream(youtubeChannelId, streamData) {
        try {
            const res = await this._PUT(`/youtube/update/${youtubeChannelId}`, {
                body: JSON.stringify(streamData),
                headers: {
                    'Content-Type': 'application/json',
                },
                expect: 'json',
            });

            if (res.err) {
                console.error("Error updating YouTube livestream:", res.err);
                throw new Error(res.err.message || "Failed to update YouTube livestream");
            }

            return res.val;
        } catch (error) {
            console.error("Error updating YouTube livestream:", error);
            throw error;
        }
    }

    async DeleteYoutubeLivestream(youtubeChannelId) {
        try {
            const res = await this._DELETE(`/youtube/delete/${youtubeChannelId}`);
            if (res.err) {
                console.error("Error deleting YouTube livestream:", res.err);
                throw new Error(res.err.message || "Failed to delete YouTube livestream");
            }
            return res.val; // Mungkin kembalikan beberapa data dari respons, atau hanya true/false
        } catch (error) {
            console.error("Error deleting YouTube livestream:", error);
            throw error;
        }
    }
}





export default API;