package monitoring

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

// MonitoringData struktur data yang akan dikirim ke monitoring server
type MonitoringData struct {
	BackendID               string      `json:"backendId"`
	Name                    string      `json:"name"`
	Email                   string      `json:"email"`
	OrderID                 string      `json:"orderId"`
	RemainingDuration       int64       `json:"remainingDuration"`
	LiveActive              bool        `json:"liveActive"`
	CPUUsage                float64     `json:"cpuUsage"`
	YoutubeTokensRemaining  int         `json:"youtubeTokensRemaining"`
	DiskInfo                DiskInfo    `json:"diskInfo"`
	MemoryInfo              MemoryInfo  `json:"memoryInfo"`
	ActiveSchedules         []string    `json:"activeSchedules"`
}

type DiskInfo struct {
	Total      uint64  `json:"total"`
	Used       uint64  `json:"used"`
	Free       uint64  `json:"free"`
	Percentage float64 `json:"percentage"`
}

type MemoryInfo struct {
	Total      uint64  `json:"total"`
	Used       uint64  `json:"used"`
	Free       uint64  `json:"free"`
	Percentage float64 `json:"percentage"`
}

// MonitoringService interface untuk monitoring
type MonitoringService interface {
	SendData(data MonitoringData) error
	StartPeriodicSending(interval time.Duration)
	StopPeriodicSending()
	GetSystemInfo() (DiskInfo, MemoryInfo, float64, error)
}

// MonitoringClient implementasi monitoring service
type MonitoringClient struct {
	monitoringServerURL string
	httpClient          *http.Client
	ticker              *time.Ticker
	stopChan            chan bool
	isRunning           bool
	
	// Data yang akan dikirim
	backendID    string
	name         string
	email        string
	orderID      string
}

// NewMonitoringClient membuat instance baru monitoring client
func NewMonitoringClient(serverURL, backendID, name, email, orderID string) *MonitoringClient {
	return &MonitoringClient{
		monitoringServerURL: serverURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		stopChan:  make(chan bool),
		backendID: backendID,
		name:      name,
		email:     email,
		orderID:   orderID,
	}
}

// GetSystemInfo mengambil informasi sistem (CPU, Memory, Disk)
func (mc *MonitoringClient) GetSystemInfo() (DiskInfo, MemoryInfo, float64, error) {
	// CPU Usage
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return DiskInfo{}, MemoryInfo{}, 0, fmt.Errorf("failed to get CPU usage: %w", err)
	}
	
	var cpuUsage float64
	if len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// Memory Info
	memStat, err := mem.VirtualMemory()
	if err != nil {
		return DiskInfo{}, MemoryInfo{}, 0, fmt.Errorf("failed to get memory info: %w", err)
	}

	memoryInfo := MemoryInfo{
		Total:      memStat.Total,
		Used:       memStat.Used,
		Free:       memStat.Available,
		Percentage: memStat.UsedPercent,
	}

	// Disk Info (root partition)
	diskStat, err := disk.Usage("/")
	if err != nil {
		return DiskInfo{}, MemoryInfo{}, 0, fmt.Errorf("failed to get disk info: %w", err)
	}

	diskInfo := DiskInfo{
		Total:      diskStat.Total,
		Used:       diskStat.Used,
		Free:       diskStat.Free,
		Percentage: diskStat.UsedPercent,
	}

	return diskInfo, memoryInfo, cpuUsage, nil
}

// SendData mengirim data monitoring ke server
func (mc *MonitoringClient) SendData(data MonitoringData) error {
	// Set backend info
	data.BackendID = mc.backendID
	data.Name = mc.name
	data.Email = mc.email
	data.OrderID = mc.orderID

	// Convert to JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal monitoring data: %w", err)
	}

	// Send HTTP POST request
	resp, err := mc.httpClient.Post(
		mc.monitoringServerURL+"/api/monitor/data",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return fmt.Errorf("failed to send monitoring data: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("monitoring server returned status: %d", resp.StatusCode)
	}

	log.Printf("📊 Monitoring data sent successfully to %s", mc.monitoringServerURL)
	return nil
}

// StartPeriodicSending memulai pengiriman data secara periodik
func (mc *MonitoringClient) StartPeriodicSending(interval time.Duration) {
	if mc.isRunning {
		log.Println("⚠️ Monitoring is already running")
		return
	}

	mc.ticker = time.NewTicker(interval)
	mc.isRunning = true

	log.Printf("🚀 Starting periodic monitoring every %v", interval)

	go func() {
		for {
			select {
			case <-mc.ticker.C:
				// Ambil system info
				diskInfo, memoryInfo, cpuUsage, err := mc.GetSystemInfo()
				if err != nil {
					log.Printf("❌ Failed to get system info: %v", err)
					continue
				}

				// TODO: Implementasi fungsi untuk mendapatkan data spesifik aplikasi
				// Untuk sementara menggunakan data dummy
				data := MonitoringData{
					RemainingDuration:      mc.getRemainingDuration(),
					LiveActive:             mc.isLiveActive(),
					CPUUsage:               cpuUsage,
					YoutubeTokensRemaining: mc.getYoutubeTokens(),
					DiskInfo:               diskInfo,
					MemoryInfo:             memoryInfo,
					ActiveSchedules:        mc.getActiveSchedules(),
				}

				// Kirim data
				if err := mc.SendData(data); err != nil {
					log.Printf("❌ Failed to send monitoring data: %v", err)
				}

			case <-mc.stopChan:
				log.Println("🛑 Stopping periodic monitoring")
				return
			}
		}
	}()
}

// StopPeriodicSending menghentikan pengiriman data periodik
func (mc *MonitoringClient) StopPeriodicSending() {
	if !mc.isRunning {
		return
	}

	mc.ticker.Stop()
	mc.stopChan <- true
	mc.isRunning = false
	log.Println("✅ Periodic monitoring stopped")
}

// Helper functions - implementasi sesuai dengan aplikasi Anda
func (mc *MonitoringClient) getRemainingDuration() int64 {
	// TODO: Implementasi untuk mendapatkan sisa durasi dari database/config
	// Contoh: return time.Until(expirationTime).Seconds()
	return 3600 // 1 hour dummy data
}

func (mc *MonitoringClient) isLiveActive() bool {
	// TODO: Implementasi untuk mengecek apakah ada live stream aktif
	// Contoh: cek dari restream store atau database
	return false // dummy data
}

func (mc *MonitoringClient) getYoutubeTokens() int {
	// TODO: Implementasi untuk mendapatkan sisa token YouTube
	// Contoh: ambil dari YouTube service
	return 1000 // dummy data
}

func (mc *MonitoringClient) getActiveSchedules() []string {
	// TODO: Implementasi untuk mendapatkan jadwal aktif
	// Contoh: ambil dari schedule manager
	return []string{"Schedule 1", "Schedule 2"} // dummy data
}

// GetMonitoringClientFromEnv membuat monitoring client dari environment variables
func GetMonitoringClientFromEnv() *MonitoringClient {
	serverURL := os.Getenv("MONITORING_SERVER_URL")
	if serverURL == "" {
		serverURL = "http://localhost:3001" // default
	}

	backendID := os.Getenv("BACKEND_ID")
	if backendID == "" {
		backendID = fmt.Sprintf("backend-%d", time.Now().Unix())
	}

	name := os.Getenv("BACKEND_NAME")
	if name == "" {
		name = "Upstream Backend"
	}

	email := os.Getenv("USER_EMAIL")
	if email == "" {
		email = "<EMAIL>"
	}

	orderID := os.Getenv("ORDER_ID")
	if orderID == "" {
		orderID = "ORDER-" + fmt.Sprintf("%d", time.Now().Unix())
	}

	return NewMonitoringClient(serverURL, backendID, name, email, orderID)
}
