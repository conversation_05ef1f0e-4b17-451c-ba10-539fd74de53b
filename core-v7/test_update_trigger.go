package main

import (
	"fmt"
	"time"
)

// Test untuk memverifikasi kondisi update
func testUpdateConditions() {
	fmt.Println("=== Testing Update Conditions ===")
	
	// Simulasi data dari db.json
	settingsMap := map[string]interface{}{
		"schedule_enabled": true,
		"repeatSchedule": "custom",
		"customScheduleDates": []interface{}{
			"2025-07-17",
			"2025-07-18", 
			"2025-07-19",
		},
		"start_time": "2025-07-15T20:00",
		"end_time": "2025-07-15T20:03",
		"title": "Test Stream",
	}
	
	// Simulasi process data dari stream_data
	processData := map[string]interface{}{
		"order": "stop",
		"start_time": "2025-07-15T13:00:00.000Z",
		"end_time": "2025-07-15T13:03:00.000Z",
	}
	
	// Simulasi waktu saat ini
	location, _ := time.LoadLocation("Asia/Jakarta")
	currentTime := time.Now().In(location)
	
	fmt.Printf("Current time: %s\n", currentTime.Format("2006-01-02 15:04:05"))
	
	// Test kondisi 1: schedule_enabled
	scheduleEnabled, exists := settingsMap["schedule_enabled"].(bool)
	fmt.Printf("schedule_enabled exists: %v, value: %v\n", exists, scheduleEnabled)
	
	// Test kondisi 2: repeatSchedule
	repeatSchedule := settingsMap["repeatSchedule"].(string)
	fmt.Printf("repeatSchedule: %s\n", repeatSchedule)
	
	// Test kondisi 3: Grace period
	timeLayout := "2006-01-02T15:04"
	settingsEndTimeStr := settingsMap["end_time"].(string)
	settingsEndTime, err := time.ParseInLocation(timeLayout, settingsEndTimeStr, location)
	if err != nil {
		fmt.Printf("Error parsing end_time: %v\n", err)
		return
	}
	settingsEndTime = settingsEndTime.UTC()
	
	gracePeriodEndTime := settingsEndTime.Add(1 * time.Minute)
	isGracePeriodPassed := currentTime.UTC().After(gracePeriodEndTime)
	
	fmt.Printf("Settings end_time: %s\n", settingsEndTimeStr)
	fmt.Printf("Grace period end: %s\n", gracePeriodEndTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Grace period passed: %v\n", isGracePeriodPassed)
	
	// Test kondisi 4: Process stopped
	processOrder := processData["order"].(string)
	isProcessStopped := (processOrder == "stop" || processOrder == "")
	fmt.Printf("Process order: %s\n", processOrder)
	fmt.Printf("Process stopped: %v\n", isProcessStopped)
	
	// Test kondisi 5: Custom dates
	customDatesInterface := settingsMap["customScheduleDates"]
	customDatesSlice := customDatesInterface.([]interface{})
	var customDates []string
	for _, dateInterface := range customDatesSlice {
		if dateStr, ok := dateInterface.(string); ok {
			customDates = append(customDates, dateStr)
		}
	}
	fmt.Printf("Custom dates: %v\n", customDates)
	
	// Test kondisi 6: Next date calculation
	startTimeStr := settingsMap["start_time"].(string)
	startTime, _ := time.ParseInLocation(timeLayout, startTimeStr, location)
	
	var nextDate *time.Time
	for _, dateStr := range customDates {
		customDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			continue
		}

		customDateTime := time.Date(
			customDate.Year(), customDate.Month(), customDate.Day(),
			startTime.Hour(), startTime.Minute(), startTime.Second(), 0,
			location,
		)

		if customDateTime.After(currentTime) {
			if nextDate == nil || customDateTime.Before(*nextDate) {
				nextDate = &customDateTime
			}
		}
	}
	
	if nextDate != nil {
		fmt.Printf("Next custom date: %s\n", nextDate.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Printf("No future custom dates found\n")
	}
	
	// Final decision
	shouldUpdate := scheduleEnabled && 
		repeatSchedule != "off" && 
		isGracePeriodPassed && 
		isProcessStopped && 
		nextDate != nil
		
	fmt.Printf("\n=== FINAL DECISION ===\n")
	fmt.Printf("Should update: %v\n", shouldUpdate)
	
	if shouldUpdate {
		fmt.Printf("✅ Update would be triggered!\n")
		newStartTimeStr := nextDate.In(location).Format(timeLayout)
		duration := settingsEndTime.Sub(startTime.UTC())
		newEndTime := nextDate.Add(duration)
		newEndTimeStr := newEndTime.In(location).Format(timeLayout)
		
		fmt.Printf("New start_time: %s\n", newStartTimeStr)
		fmt.Printf("New end_time: %s\n", newEndTimeStr)
	} else {
		fmt.Printf("❌ Update would NOT be triggered\n")
		fmt.Printf("Reasons:\n")
		if !scheduleEnabled { fmt.Printf("- Schedule not enabled\n") }
		if repeatSchedule == "off" { fmt.Printf("- Repeat schedule is off\n") }
		if !isGracePeriodPassed { fmt.Printf("- Grace period not passed\n") }
		if !isProcessStopped { fmt.Printf("- Process not stopped\n") }
		if nextDate == nil { fmt.Printf("- No future custom dates\n") }
	}
}

func main() {
	testUpdateConditions()
}
