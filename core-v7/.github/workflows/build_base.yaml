name: "Build main base"

on:
    workflow_dispatch:
    workflow_call:
    push:
        branches-ignore:
            - "**"

jobs:
    versions:
        runs-on: ubuntu-latest
        outputs:
            coreversion: ${{ steps.core.outputs.version }}
        steps:
            - name: Checkout core repo
              uses: actions/checkout@v4
              with:
                  repository: datarhei/core
                  path: ./core

            - name: Get latest version from core
              id: core
              run: |
                  echo "version=$(cat ./core/app/version.go | grep -E -o '(Major|Minor|Patch): [0-9]+,' | sed -E 's/^.*: ([0-9]+),.*$/\1/g' | paste -sd '.' - )" >> "$GITHUB_OUTPUT"

            - name: Show versions
              run: |
                  echo "core: ${{ steps.core.outputs.version }}"

    docker:
        needs: versions
        runs-on: [self-hosted]
        strategy:
            matrix:
                include:
                    - core: ${{ needs.versions.outputs.coreversion }}
                      os: alpine
                      os_version: "3.20"
                      golang: golang:1.23-alpine3.20
                      platforms: linux/amd64,linux/arm64,linux/arm/v7,linux/arm/v6
                      branch: main
                      latest: yes
                    - core: ${{ needs.versions.outputs.coreversion }}
                      os: ubuntu
                      os_version: "22.04"
                      golang: golang:1.23-alpine3.20
                      platforms: linux/amd64
                      branch: main
                      latest: yes

        steps:
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  ref: ${{ matrix.branch }}

            - name: Docker meta
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: |
                      datarhei/base
                  tags: |
                      type=raw,value=core${{ matrix.core }}-${{ matrix.os }}${{ matrix.os_version }}
                      type=raw,value=${{ matrix.os }}-core-${{ matrix.os_version }}-${{ matrix.core}},enable=${{ matrix.latest == 'yes' }}
                      type=raw,value=${{ matrix.os }}-core-latest,enable=${{ matrix.latest == 'yes' }}

            - name: Set up QEMU
              uses: docker/setup-qemu-action@master
              with:
                  platforms: all

            - name: Set up Docker Buildx
              id: buildx
              uses: docker/setup-buildx-action@master

            - name: Login to DockerHub
              if: github.event_name != 'pull_request'
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Build Multi-Arch
              uses: docker/build-push-action@v5
              with:
                  builder: ${{ steps.buildx.outputs.name }}
                  context: .
                  file: ./Dockerfile
                  build-args: |
                      BUILD_IMAGE=${{ matrix.os }}:${{ matrix.os_version }}
                      GOLANG_IMAGE=${{ matrix.golang }}
                  platforms: ${{ matrix.platforms }}
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
