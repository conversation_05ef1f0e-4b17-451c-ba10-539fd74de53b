name: tests

on: [push, pull_request]

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
              with:
                  fetch-depth: 2
            - uses: actions/setup-go@v5
              with:
                  go-version: "1.22"
            - name: Run coverage
              run: go test -coverprofile=coverage.out -covermode=atomic -v ./...
            - name: Upload coverage to Codecov
              uses: codecov/codecov-action@v4
              with:
                  token: ${{ secrets.CODECOV_TOKEN }}
                  files: coverage.out
                  flags: unit-linux
