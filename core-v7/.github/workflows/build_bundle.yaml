name: "Build main bundles"

on:
    workflow_dispatch:
    workflow_call:
    push:
        branches-ignore:
            - "**"

jobs:
    versions:
        runs-on: ubuntu-latest
        outputs:
            coreversion: ${{ steps.core.outputs.version }}
        steps:
            - name: Checkout core repo
              uses: actions/checkout@v4
              with:
                  repository: datarhei/core
                  path: ./core

            - name: Get latest version from core
              id: core
              run: |
                  echo "version=$(cat ./core/app/version.go | grep -E -o '(Major|Minor|Patch): [0-9]+,' | sed -E 's/^.*: ([0-9]+),.*$/\1/g' | paste -sd '.' - )" >> "$GITHUB_OUTPUT"

            - name: Show versions
              run: |
                  echo "core: ${{ steps.core.outputs.version }}"

    docker:
        needs: versions
        runs-on: [self-hosted]
        strategy:
            matrix:
                include:
                    - core: ${{ needs.versions.outputs.coreversion }}
                      core_os: alpine3.20
                      ffmpeg: "6.1.1"
                      ffmpeg_os: alpine3.20
                      platforms: linux/amd64,linux/arm64,linux/arm/v7
                      branch: main
                      prefix:
                      latest: yes
                    - core: ${{ needs.versions.outputs.coreversion }}
                      core_os: alpine3.20
                      ffmpeg: "6.1.1-rpi"
                      ffmpeg_os: alpine3.20
                      platforms: linux/arm64,linux/arm/v7
                      branch: main
                      prefix: rpi-
                      latest: yes
                    - core: ${{ needs.versions.outputs.coreversion }}
                      core_os: ubuntu22.04
                      ffmpeg: "6.1.1-vaapi"
                      ffmpeg_os: ubuntu22.04
                      platforms: linux/amd64
                      branch: main
                      prefix: vaapi-
                      latest: yes
                    - core: ${{ needs.versions.outputs.coreversion }}
                      core_os: ubuntu22.04
                      ffmpeg: "6.1.1-cuda"
                      ffmpeg_os: ubuntu22.04
                      ffmpeg_tags: "-cuda11.7.1"
                      platforms: linux/amd64
                      branch: main
                      prefix: cuda-
                      latest: yes

        steps:
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  ref: ${{ matrix.branch }}

            - name: Docker meta
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: |
                      datarhei/core
                  tags: |
                      type=raw,value=${{ matrix.core }}-ffmpeg${{ matrix.ffmpeg }}${{ matrix.ffmpeg_tags }}-${{ matrix.core_os }}
                      type=raw,value=${{ matrix.prefix }}${{ matrix.core }},enable=${{ matrix.latest == 'yes' }}
                      type=raw,value=${{ matrix.prefix }}latest,enable=${{ matrix.latest == 'yes' }}

            - name: Set up QEMU
              uses: docker/setup-qemu-action@master
              with:
                  platforms: all

            - name: Set up Docker Buildx
              id: buildx
              uses: docker/setup-buildx-action@master

            - name: Login to DockerHub
              if: github.event_name != 'pull_request'
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Build Multi-Arch
              uses: docker/build-push-action@v5
              with:
                  builder: ${{ steps.buildx.outputs.name }}
                  context: .
                  file: ./Dockerfile.bundle
                  build-args: |
                      CORE_IMAGE=datarhei/base:core${{ matrix.core }}-${{ matrix.core_os }}
                      FFMPEG_IMAGE=datarhei/base:ffmpeg${{ matrix.ffmpeg }}-${{ matrix.ffmpeg_os}}${{ matrix.ffmpeg_tags }}
                  platforms: ${{ matrix.platforms }}
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
