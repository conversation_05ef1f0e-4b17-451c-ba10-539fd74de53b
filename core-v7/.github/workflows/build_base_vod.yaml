name: "Build vod base"

on:
    workflow_dispatch:
    workflow_call:
    push:
        branches:
            - vod

jobs:
    docker:
        runs-on: [self-hosted]
        strategy:
            matrix:
                include:
                    - os: alpine
                      os_version: "3.20"
                      golang: golang:1.23-alpine3.20
                      platforms: linux/amd64,linux/arm64,linux/arm/v7
                      branch: vod
                    - os: ubuntu
                      os_version: "24.04"
                      golang: golang:1.23-alpine3.20
                      platforms: linux/amd64
                      branch: vod

        steps:
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  ref: ${{ matrix.branch }}

            - name: Docker meta
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: |
                      datarhei/base
                  tags: |
                      type=raw,value=core-${{ matrix.branch }}-${{ matrix.os }}${{ matrix.os_version }}

            - name: Set up QEMU
              uses: docker/setup-qemu-action@master
              with:
                  platforms: all

            - name: Set up Docker Buildx
              id: buildx
              uses: docker/setup-buildx-action@master

            - name: Login to DockerHub
              if: github.event_name != 'pull_request'
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Build Multi-Arch
              uses: docker/build-push-action@v5
              with:
                  builder: ${{ steps.buildx.outputs.name }}
                  context: .
                  file: ./Dockerfile
                  build-args: |
                      BUILD_IMAGE=${{ matrix.os }}:${{ matrix.os_version }}
                      GOLANG_IMAGE=${{ matrix.golang }}
                  platforms: ${{ matrix.platforms }}
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
