{"version": 4, "process": {"upstream-id:egress:youtube:5eb0d4c0-5c97-4e29-945c-4dfb33801608": {"id": "upstream-id:egress:youtube:5eb0d4c0-5c97-4e29-945c-4dfb33801608", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-07-15T17:08", "end_time": "2025-07-15T17:09", "config": {"id": "upstream-id:egress:youtube:5eb0d4c0-5c97-4e29-945c-4dfb33801608", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-07-15T17:08", "end_time": "2025-07-15T17:09", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "options": ["-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "rtmps://a.rtmp.youtube.com/live2/hf03-eup4-9xtt-64cj-5kux", "options": ["-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "cleanup": []}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": false, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1752574072, "updated_at": 1752574072, "order": "stop"}, "upstream-id:egress:youtube:80a3d9d3-a611-4901-ac7e-d698ac09ca4a": {"id": "upstream-id:egress:youtube:80a3d9d3-a611-4901-ac7e-d698ac09ca4a", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-07-15T19:49", "end_time": "2025-07-15T19:51", "config": {"id": "upstream-id:egress:youtube:80a3d9d3-a611-4901-ac7e-d698ac09ca4a", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-07-15T19:49", "end_time": "2025-07-15T19:51", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "options": ["-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "rtmps://a.rtmp.youtube.com/live2/f6fp-bxvr-j53a-802u-5fgk", "options": ["-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "cleanup": []}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": false, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1752583695, "updated_at": 1752583695, "order": "start"}, "upstream-id:egress:youtube:ea72bff1-adfe-49f8-91c6-d2b02047a24f": {"id": "upstream-id:egress:youtube:ea72bff1-adfe-49f8-91c6-d2b02047a24f", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-08-15T17:22", "end_time": "2025-08-15T17:24", "config": {"id": "upstream-id:egress:youtube:ea72bff1-adfe-49f8-91c6-d2b02047a24f", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "2025-08-15T17:22", "end_time": "2025-08-15T17:24", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "options": ["-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "rtmps://a.rtmp.youtube.com/live2/u8wt-zaas-j0tq-e9ft-d5pg", "options": ["-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "cleanup": []}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": false, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1752574866, "updated_at": 1752575157, "order": "stop"}, "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985": {"id": "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985", "reference": "39ede332-5c78-43b3-ab2b-b00a196ea985", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985", "reference": "39ede332-5c78-43b3-ab2b-b00a196ea985", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "data/channels/39ede332-5c78-43b3-ab2b-b00a196ea985/videoloop.source", "options": ["-stream_loop", "-1", "-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/39ede332-5c78-43b3-ab2b-b00a196ea985_{outputid}.m3u8", "options": ["-dn", "-sn", "-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-metadata", "title=http://**************:8080/39ede332-5c78-43b3-ab2b-b00a196ea985/oembed.json", "-metadata", "service_provider=Upstreamid-Upstreamid", "-f", "hls", "-start_number", "0", "-hls_time", "2", "-hls_list_size", "6", "-hls_flags", "append_list+delete_segments+program_date_time+temp_file", "-hls_delete_threshold", "4", "-hls_segment_filename", "{memfs}/39ede332-5c78-43b3-ab2b-b00a196ea985_{outputid}_%04d.ts", "-master_pl_name", "39ede332-5c78-43b3-ab2b-b00a196ea985.m3u8", "-master_pl_publish_rate", "2", "-method", "PUT"], "cleanup": [{"pattern": "memfs:/39ede332-5c78-43b3-ab2b-b00a196ea985**", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}, {"pattern": "memfs:/39ede332-5c78-43b3-ab2b-b00a196ea985_{outputid}.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/39ede332-5c78-43b3-ab2b-b00a196ea985_{outputid}_**.ts", "max_files": 12, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/39ede332-5c78-43b3-ab2b-b00a196ea985.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}]}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err", "-y"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1747474053, "updated_at": 1747474053, "order": "stop"}, "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985_snapshot": {"id": "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985_snapshot", "reference": "39ede332-5c78-43b3-ab2b-b00a196ea985", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985_snapshot", "reference": "39ede332-5c78-43b3-ab2b-b00a196ea985", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/39ede332-5c78-43b3-ab2b-b00a196ea985.m3u8", "options": [], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/39ede332-5c78-43b3-ab2b-b00a196ea985.jpg", "options": ["-vframes", "1", "-f", "image2", "-update", "1"], "cleanup": [{"pattern": "memfs:/39ede332-5c78-43b3-ab2b-b00a196ea985.jpg", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}]}], "options": ["-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 60, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 0}, "created_at": 1747474054, "updated_at": 1747474054, "order": "stop"}, "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a": {"id": "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a", "reference": "de93673d-1796-4f58-93c6-dd41d4778b6a", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a", "reference": "de93673d-1796-4f58-93c6-dd41d4778b6a", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "data/channels/de93673d-1796-4f58-93c6-dd41d4778b6a/videoloop.source", "options": ["-stream_loop", "-1", "-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/de93673d-1796-4f58-93c6-dd41d4778b6a_{outputid}.m3u8", "options": ["-dn", "-sn", "-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-metadata", "title=http://**************:8080/de93673d-1796-4f58-93c6-dd41d4778b6a/oembed.json", "-metadata", "service_provider=Upstreamid-Upstreamid", "-f", "hls", "-start_number", "0", "-hls_time", "2", "-hls_list_size", "6", "-hls_flags", "append_list+delete_segments+program_date_time+temp_file", "-hls_delete_threshold", "4", "-hls_segment_filename", "{memfs}/de93673d-1796-4f58-93c6-dd41d4778b6a_{outputid}_%04d.ts", "-master_pl_name", "de93673d-1796-4f58-93c6-dd41d4778b6a.m3u8", "-master_pl_publish_rate", "2", "-method", "PUT"], "cleanup": [{"pattern": "memfs:/de93673d-1796-4f58-93c6-dd41d4778b6a**", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}, {"pattern": "memfs:/de93673d-1796-4f58-93c6-dd41d4778b6a_{outputid}.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/de93673d-1796-4f58-93c6-dd41d4778b6a_{outputid}_**.ts", "max_files": 12, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/de93673d-1796-4f58-93c6-dd41d4778b6a.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}]}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err", "-y"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1747474395, "updated_at": 1747474395, "order": "start"}, "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a_snapshot": {"id": "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a_snapshot", "reference": "de93673d-1796-4f58-93c6-dd41d4778b6a", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a_snapshot", "reference": "de93673d-1796-4f58-93c6-dd41d4778b6a", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/de93673d-1796-4f58-93c6-dd41d4778b6a.m3u8", "options": [], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/de93673d-1796-4f58-93c6-dd41d4778b6a.jpg", "options": ["-vframes", "1", "-f", "image2", "-update", "1"], "cleanup": [{"pattern": "memfs:/de93673d-1796-4f58-93c6-dd41d4778b6a.jpg", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}]}], "options": ["-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 60, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 0}, "created_at": 1747474396, "updated_at": 1747474396, "order": "start"}, "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f": {"id": "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "data/channels/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/videoloop.source", "options": ["-stream_loop", "-1", "-re"], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f_{outputid}.m3u8", "options": ["-dn", "-sn", "-map", "0:0", "-codec:v", "copy", "-map", "0:1", "-codec:a", "copy", "-metadata", "title=http://**************:8080/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/oembed.json", "-metadata", "service_provider=Upstreamid-Upstreamid", "-f", "hls", "-start_number", "0", "-hls_time", "2", "-hls_list_size", "6", "-hls_flags", "append_list+delete_segments+program_date_time+temp_file", "-hls_delete_threshold", "4", "-hls_segment_filename", "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f_{outputid}_%04d.ts", "-master_pl_name", "f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "-master_pl_publish_rate", "2", "-method", "PUT"], "cleanup": [{"pattern": "memfs:/f2b55dec-f6d2-4e72-b63d-52ef279cf36f**", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}, {"pattern": "memfs:/f2b55dec-f6d2-4e72-b63d-52ef279cf36f_{outputid}.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/f2b55dec-f6d2-4e72-b63d-52ef279cf36f_{outputid}_**.ts", "max_files": 12, "max_file_age_seconds": 24, "purge_on_delete": true}, {"pattern": "memfs:/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "max_files": 0, "max_file_age_seconds": 24, "purge_on_delete": true}]}], "options": ["-loglevel", "level+info", "-err_detect", "ignore_err", "-y"], "reconnect": true, "reconnect_delay_seconds": 5, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 5}, "created_at": 1752480466, "updated_at": 1752480466, "order": "start"}, "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f_snapshot": {"id": "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f_snapshot", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "", "end_time": "", "config": {"id": "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f_snapshot", "reference": "f2b55dec-f6d2-4e72-b63d-52ef279cf36f", "start_time": "", "end_time": "", "ffversion": "^6.1.1", "input": [{"id": "input_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.m3u8", "options": [], "cleanup": []}], "output": [{"id": "output_0", "address": "{memfs}/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.jpg", "options": ["-vframes", "1", "-f", "image2", "-update", "1"], "cleanup": [{"pattern": "memfs:/f2b55dec-f6d2-4e72-b63d-52ef279cf36f.jpg", "max_files": 0, "max_file_age_seconds": 0, "purge_on_delete": true}]}], "options": ["-err_detect", "ignore_err"], "reconnect": true, "reconnect_delay_seconds": 60, "autostart": true, "stale_timeout_seconds": 30, "limit_cpu_usage": 0, "limit_memory_bytes": 0, "limit_waitfor_seconds": 0}, "created_at": 1752480467, "updated_at": 1752480467, "order": "stop"}}, "metadata": {"system": {"upstream-id": {"bundle": {"version": "v2.12.0"}, "playersite": {}, "version": "1.14.0"}}, "process": {"upstream-id:egress:youtube:5eb0d4c0-5c97-4e29-945c-4dfb33801608": {"upstream-id": {"control": {"limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": false, "delay": 5, "reconnect": true, "staleTimeout": 30}, "source": {"source": "hls+memfs"}}, "name": "Youtube Live", "outputs": [{"address": "rtmps://a.rtmp.youtube.com/live2/hf03-eup4-9xtt-64cj-5kux", "autoGenerateTitle": true, "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "description": "", "end_time": "2025-07-15T17:09", "manualTitleRotating": false, "manualTitles": "", "options": ["-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "privacy": "public", "repeatSchedule": "daily", "schedule_repeat": 0, "start_time": "2025-07-15T17:08", "stream_key": "hf03-eup4-9xtt-64cj-5kux", "stream_name": "UPSTREAM-6", "tags": "", "thumbnail": "", "title": "Discovering the Secret Treasures of Rainswn: The Ultimate Exploration Journey!", "youtube_api": true, "youtube_channel": "UC5Ax7pD7G3SQQVA5_jbDoww"}], "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": -1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "settings": {"TokenAPI": 390, "TokenChatbot": 0, "autoGenerateTitle": true, "backup": false, "channel": "UC5Ax7pD7G3SQQVA5_jbDoww", "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "contentId": "", "description": "", "duration": "", "end_time": "2025-07-16T17:09", "manualTitleRotating": false, "manualTitles": "", "mode": "rtmps", "primary": true, "privacy": "public", "repeatCount": 1, "repeatSchedule": "daily", "scheduleRepeat": false, "schedule_enabled": true, "schedule_repeat": 0, "start_time": "2025-07-16T17:08", "stream_key": "hf03-eup4-9xtt-64cj-5kux", "stream_name": "UPSTREAM-6", "tags": "", "thumbnail": "", "title": "Discovering the Secret Treasures of Rainswn: The Ultimate Exploration Journey!", "userTimezone": "Asia/Bangkok", "youtube_api": true}, "streams": [{"channels": 0, "codec": "h264", "height": 1080, "index": 0, "layout": "", "pix_fmt": "", "sampling_hz": 0, "stream": 0, "type": "video", "url": "", "width": 1920}, {"channels": 0, "codec": "aac", "height": 0, "index": 0, "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "", "width": 0}], "version": "1.14.0"}}, "upstream-id:egress:youtube:80a3d9d3-a611-4901-ac7e-d698ac09ca4a": {"upstream-id": {"control": {"limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": false, "delay": 5, "reconnect": true, "staleTimeout": 30}, "source": {"source": "hls+memfs"}}, "name": "Youtube Live", "outputs": [{"address": "rtmps://a.rtmp.youtube.com/live2/f6fp-bxvr-j53a-802u-5fgk", "autoGenerateTitle": true, "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "customScheduleDates": ["2025-07-17", "2025-07-18"], "description": "", "end_time": "2025-07-15T19:51", "manualTitleRotating": false, "manualTitles": "CC\nVV", "options": ["-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "privacy": "public", "repeatSchedule": "custom", "schedule_repeat": 0, "start_time": "2025-07-15T19:49", "stream_key": "f6fp-bxvr-j53a-802u-5fgk", "stream_name": "UPSTREAM-5", "tags": "", "thumbnail": "", "title": "Rain White Thunder", "youtube_api": true, "youtube_channel": "UC5Ax7pD7G3SQQVA5_jbDoww"}], "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": -1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "settings": {"TokenAPI": 360, "TokenChatbot": 0, "autoGenerateTitle": true, "backup": false, "channel": "UC5Ax7pD7G3SQQVA5_jbDoww", "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "contentId": "", "customScheduleDates": ["2025-07-17", "2025-07-18"], "description": "", "duration": "", "end_time": "2025-07-15T19:51", "manualTitleRotating": false, "manualTitles": "CC\nVV", "mode": "rtmps", "primary": true, "privacy": "public", "repeatCount": 1, "repeatSchedule": "custom", "scheduleRepeat": false, "schedule_enabled": true, "schedule_repeat": 0, "start_time": "2025-07-15T19:49", "stream_key": "f6fp-bxvr-j53a-802u-5fgk", "stream_name": "UPSTREAM-5", "tags": "", "thumbnail": "", "title": "Rain White Thunder", "userTimezone": "Asia/Bangkok", "youtube_api": true}, "streams": [{"channels": 0, "codec": "h264", "height": 1080, "index": 0, "layout": "", "pix_fmt": "", "sampling_hz": 0, "stream": 0, "type": "video", "url": "", "width": 1920}, {"channels": 0, "codec": "aac", "height": 0, "index": 0, "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "", "width": 0}], "version": "1.14.0"}}, "upstream-id:egress:youtube:ea72bff1-adfe-49f8-91c6-d2b02047a24f": {"upstream-id": {"control": {"limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": false, "delay": 5, "reconnect": true, "staleTimeout": 30}, "source": {"source": "hls+memfs"}}, "name": "Youtube Live", "outputs": [{"address": "rtmps://a.rtmp.youtube.com/live2/u8wt-zaas-j0tq-e9ft-d5pg", "autoGenerateTitle": true, "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "description": "", "end_time": "2025-07-15T17:24", "manualTitleRotating": false, "manualTitles": "", "options": ["-f", "flv", "-rtmp_enhanced_codecs", "hvc1,av01"], "privacy": "public", "repeatSchedule": "monthly", "schedule_repeat": 0, "start_time": "2025-07-15T17:22", "stream_key": "u8wt-zaas-j0tq-e9ft-d5pg", "stream_name": "UPSTREAM-4", "tags": "", "thumbnail": "", "title": "<PERSON><PERSON><PERSON>", "youtube_api": true, "youtube_channel": "UC5Ax7pD7G3SQQVA5_jbDoww"}], "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": -1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "settings": {"TokenAPI": 390, "TokenChatbot": 0, "autoGenerateTitle": true, "backup": false, "channel": "UC5Ax7pD7G3SQQVA5_jbDoww", "chatbot": false, "chatbot_keyword": "Anda adalah layanan livechat untuk youtube saya yang bertemakan ASMR suara <PERSON>, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan anda menjadi seolah-olah ornag asli yang memiliki channel ini, jangan berikan informasi bahwa anda adalah AI, Pastikan menjadwab dalam bahasa sesuai dengan pertanyaan. tambahkan juga emoticon yang di dukung live chat youtube.", "contentId": "", "description": "", "duration": "", "end_time": "2025-08-15T17:24", "manualTitleRotating": false, "manualTitles": "", "mode": "rtmps", "primary": true, "privacy": "public", "repeatCount": 1, "repeatSchedule": "monthly", "scheduleRepeat": false, "schedule_enabled": true, "schedule_repeat": 0, "start_time": "2025-08-15T17:22", "stream_key": "u8wt-zaas-j0tq-e9ft-d5pg", "stream_name": "UPSTREAM-4", "tags": "", "thumbnail": "", "title": "<PERSON><PERSON><PERSON>", "userTimezone": "Asia/Bangkok", "youtube_api": true}, "streams": [], "version": "1.14.0"}}, "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985": {"upstream-id": {"control": {"hls": {"cleanup": true, "lhls": false, "listSize": 6, "master_playlist": true, "segmentDuration": 2, "storage": "memfs", "version": 3}, "limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": true, "delay": 5, "low_delay": false, "reconnect": true, "staleTimeout": 30}, "rtmp": {"enable": false}, "snapshot": {"enable": true, "interval": 60}, "srt": {"enable": false}}, "license": "CC BY 4.0", "meta": {"author": {"description": "", "name": ""}, "description": "", "name": "Live 5"}, "player": {}, "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": 1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "sources": [{"inputs": [{"address": "data/channels/39ede332-5c78-43b3-ab2b-b00a196ea985/videoloop.source", "options": ["-stream_loop", "-1", "-re"]}], "settings": {"address": "data/channels/39ede332-5c78-43b3-ab2b-b00a196ea985/videoloop.source", "mimetype": "video/mp4"}, "streams": [{"bitrate_kbps": 4405, "channels": 0, "codec": "h264", "coder": "", "duration_sec": 416.67, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 24, "height": 1080, "index": 0, "language": "eng", "layout": "", "pix_fmt": "yuv420p", "sampling_hz": 0, "stream": 0, "type": "video", "url": "data/channels/39ede332-5c78-43b3-ab2b-b00a196ea985/videoloop.source", "width": 1920}, {"bitrate_kbps": 125, "channels": 0, "codec": "aac", "coder": "", "duration_sec": 416.67, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 0, "height": 0, "index": 0, "language": "eng", "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "data/channels/39ede332-5c78-43b3-ab2b-b00a196ea985/videoloop.source", "width": 0}], "type": "videoloop"}, {"inputs": [], "settings": {}, "streams": [], "type": "videoaudio"}], "streams": [{"channels": 0, "codec": "h264", "height": 1080, "index": 0, "layout": "", "pix_fmt": "", "sampling_hz": 0, "stream": 0, "type": "video", "url": "", "width": 1920}, {"channels": 0, "codec": "aac", "height": 0, "index": 0, "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "", "width": 0}], "version": "1.14.0"}}, "upstream-id:ingest:39ede332-5c78-43b3-ab2b-b00a196ea985_snapshot": null, "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a": {"upstream-id": {"control": {"hls": {"cleanup": true, "lhls": false, "listSize": 6, "master_playlist": true, "segmentDuration": 2, "storage": "memfs", "version": 3}, "limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": true, "delay": 5, "low_delay": false, "reconnect": true, "staleTimeout": 30}, "rtmp": {"enable": false}, "snapshot": {"enable": true, "interval": 60}, "srt": {"enable": false}}, "license": "CC BY 4.0", "meta": {"author": {"description": "", "name": ""}, "description": "", "name": "Live 7"}, "player": {}, "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": 1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "sources": [{"inputs": [{"address": "data/channels/de93673d-1796-4f58-93c6-dd41d4778b6a/videoloop.source", "options": ["-stream_loop", "-1", "-re"]}], "settings": {"address": "data/channels/de93673d-1796-4f58-93c6-dd41d4778b6a/videoloop.source", "mimetype": "video/mp4"}, "streams": [{"bitrate_kbps": 4035, "channels": 0, "codec": "h264", "coder": "", "duration_sec": 60, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 24, "height": 1080, "index": 0, "language": "eng", "layout": "", "pix_fmt": "yuv420p", "sampling_hz": 0, "stream": 0, "type": "video", "url": "data/channels/de93673d-1796-4f58-93c6-dd41d4778b6a/videoloop.source", "width": 1920}, {"bitrate_kbps": 125, "channels": 0, "codec": "aac", "coder": "", "duration_sec": 60, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 0, "height": 0, "index": 0, "language": "eng", "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "data/channels/de93673d-1796-4f58-93c6-dd41d4778b6a/videoloop.source", "width": 0}], "type": "videoloop"}, {"inputs": [], "settings": {}, "streams": [], "type": "videoaudio"}], "streams": [{"channels": 0, "codec": "h264", "height": 1080, "index": 0, "layout": "", "pix_fmt": "", "sampling_hz": 0, "stream": 0, "type": "video", "url": "", "width": 1920}, {"channels": 0, "codec": "aac", "height": 0, "index": 0, "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "", "width": 0}], "version": "1.14.0"}}, "upstream-id:ingest:de93673d-1796-4f58-93c6-dd41d4778b6a_snapshot": null, "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f": {"upstream-id": {"control": {"hls": {"cleanup": true, "lhls": false, "listSize": 6, "master_playlist": true, "segmentDuration": 2, "storage": "memfs", "version": 3}, "limits": {"cpu_usage": 0, "memory_mbytes": 0, "waitfor_seconds": 5}, "process": {"autostart": true, "delay": 5, "low_delay": false, "reconnect": true, "staleTimeout": 30}, "rtmp": {"enable": false}, "snapshot": {"enable": true, "interval": 60}, "srt": {"enable": false}}, "license": "CC BY 4.0", "meta": {"author": {"description": "", "name": ""}, "description": "", "name": "A"}, "player": {}, "profiles": [{"audio": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:a", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 1}, "custom": {"selected": false, "stream": 1}, "video": {"decoder": {"coder": "default", "mapping": {"filter": [], "global": [], "local": []}, "settings": {}}, "encoder": {"coder": "copy", "mapping": {"filter": [], "global": [], "local": ["-codec:v", "copy"]}, "settings": {}}, "filter": {"graph": "", "settings": {}}, "source": 0, "stream": 0}}], "sources": [{"inputs": [{"address": "data/channels/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/videoloop.source", "options": ["-stream_loop", "-1", "-re"]}], "settings": {"address": "data/channels/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/videoloop.source", "mimetype": "video/mp4"}, "streams": [{"bitrate_kbps": 4405, "channels": 0, "codec": "h264", "coder": "", "duration_sec": 416.67, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 24, "height": 1080, "index": 0, "language": "eng", "layout": "", "pix_fmt": "yuv420p", "sampling_hz": 0, "stream": 0, "type": "video", "url": "data/channels/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/videoloop.source", "width": 1920}, {"bitrate_kbps": 125, "channels": 0, "codec": "aac", "coder": "", "duration_sec": 416.67, "format": "mov,mp4,m4a,3gp,3g2,mj2", "fps": 0, "height": 0, "index": 0, "language": "eng", "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "data/channels/f2b55dec-f6d2-4e72-b63d-52ef279cf36f/videoloop.source", "width": 0}], "type": "videoloop"}, {"inputs": [], "settings": {}, "streams": [], "type": "videoaudio"}], "streams": [{"channels": 0, "codec": "h264", "height": 1080, "index": 0, "layout": "", "pix_fmt": "", "sampling_hz": 0, "stream": 0, "type": "video", "url": "", "width": 1920}, {"channels": 0, "codec": "aac", "height": 0, "index": 0, "layout": "stereo", "pix_fmt": "", "sampling_hz": 44100, "stream": 1, "type": "audio", "url": "", "width": 0}], "version": "1.14.0"}}, "upstream-id:ingest:f2b55dec-f6d2-4e72-b63d-52ef279cf36f_snapshot": null}}}