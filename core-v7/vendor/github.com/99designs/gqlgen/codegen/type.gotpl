{{ $useFunctionSyntaxForExecutionContext := .Config.UseFunctionSyntaxForExecutionContext }}
{{- range $type := .ReferencedTypes }}
	{{ with $type.UnmarshalFunc }}
		{{ if $useFunctionSyntaxForExecutionContext -}}
		func {{ . }}(ctx context.Context, ec *executionContext, v any) ({{ $type.GO | ref }}, error) {
		{{- else -}}
		func (ec *executionContext) {{ . }}(ctx context.Context, v any) ({{ $type.GO | ref }}, error) {
		{{- end -}}
			{{- if and $type.IsNilable (not $type.GQL.NonNull) (not $type.IsPtrToPtr) }}
				if v == nil { return nil, nil }
			{{- end }}
			{{- if or $type.IsPtrToSlice $type.IsPtrToIntf }}
				{{ if $useFunctionSyntaxForExecutionContext -}}
				res, err := {{ $type.Elem.UnmarshalFunc }}(ctx, ec, v)
				{{- else -}}
				res, err := ec.{{ $type.Elem.UnmarshalFunc }}(ctx, v)
				{{- end }}
				return &res, graphql.ErrorOnPath(ctx, err)
			{{- else if $type.IsSlice }}
				var vSlice []any
				if v != nil {
					vSlice = graphql.CoerceList(v)
				}
				var err error
				res := make([]{{$type.GO.Elem | ref}}, len(vSlice))
				for i := range vSlice {
					ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
					{{ if $useFunctionSyntaxForExecutionContext -}}
					res[i], err = {{ $type.Elem.UnmarshalFunc }}(ctx, ec, vSlice[i])
					{{- else -}}
					res[i], err = ec.{{ $type.Elem.UnmarshalFunc }}(ctx, vSlice[i])
					{{- end }}
					if err != nil {
						return nil, err
					}
				}
				return res, nil
			{{- else if and $type.IsPtrToPtr (not $type.Unmarshaler) (not $type.IsMarshaler) }}
				var pres {{ $type.Elem.GO | ref }}
				if v != nil {
					{{ if $useFunctionSyntaxForExecutionContext -}}
					res, err := {{ $type.Elem.UnmarshalFunc }}(ctx, ec, v)
					{{- else -}}
					res, err := ec.{{ $type.Elem.UnmarshalFunc }}(ctx, v)
					{{- end }}
					if err != nil {
						return nil, graphql.ErrorOnPath(ctx, err)
					}
					pres = res
				}
				return &pres, nil
			{{- else }}
				{{- if $type.Unmarshaler }}
					{{- if $type.HasEnumValues }}
						tmp, err := {{ $type.Unmarshaler | call }}(v)
						res := {{ $type.UnmarshalFunc }}[tmp]
					{{- else if $type.CastType }}
						{{- if $type.IsContext }}
							tmp, err := {{ $type.Unmarshaler | call }}(ctx, v)
						{{- else }}
							tmp, err := {{ $type.Unmarshaler | call }}(v)
						{{- end }}
						{{- if and $type.IsNilable $type.Elem }}
							res := {{ $type.Elem.GO | ref }}(tmp)
						{{- else}}
							res := {{ $type.GO | ref }}(tmp)
						{{- end }}
					{{- else}}
						{{- if $type.IsContext }}
							res, err := {{ $type.Unmarshaler | call }}(ctx, v)
						{{- else }}
							res, err := {{ $type.Unmarshaler | call }}(v)
						{{- end }}
					{{- end }}
					{{- if and $type.IsTargetNilable (not $type.IsNilable) }}
						return *res, graphql.ErrorOnPath(ctx, err)
					{{- else if and (not $type.IsTargetNilable) $type.IsNilable }}
						return &res, graphql.ErrorOnPath(ctx, err)
					{{- else}}
						return res, graphql.ErrorOnPath(ctx, err)
					{{- end }}
				{{- else if $type.IsMarshaler }}
					{{- if and $type.IsNilable $type.Elem }}
						var res = new({{ $type.Elem.GO | ref }})
					{{- else}}
						var res {{ $type.GO | ref }}
					{{- end }}
					{{- if $type.IsContext }}
						err := res.UnmarshalGQLContext(ctx, v)
					{{- else }}
						err := res.UnmarshalGQL(v)
					{{- end }}
					return res, graphql.ErrorOnPath(ctx, err)
				{{- else }}
					{{ if $useFunctionSyntaxForExecutionContext -}}
					res, err := unmarshalInput{{ $type.GQL.Name }}(ctx, ec, v)
					{{- else -}}
					res, err := ec.unmarshalInput{{ $type.GQL.Name }}(ctx, v)
					{{- end }}
					{{- if and $type.IsNilable (not $type.IsMap) (not $type.PointersInUnmarshalInput) }}
						return &res, graphql.ErrorOnPath(ctx, err)
					{{- else if and (not $type.IsNilable) $type.PointersInUnmarshalInput }}
						return *res, graphql.ErrorOnPath(ctx, err)
					{{- else }}
						return res, graphql.ErrorOnPath(ctx, err)
					{{- end }}
				{{- end }}
			{{- end }}
		}
	{{- end }}

	{{ with $type.MarshalFunc }}
		{{ if $useFunctionSyntaxForExecutionContext -}}
		func {{ . }}(ctx context.Context, ec *executionContext, sel ast.SelectionSet, v {{ $type.GO | ref }}) graphql.Marshaler {
		{{- else -}}
		func (ec *executionContext) {{ . }}(ctx context.Context, sel ast.SelectionSet, v {{ $type.GO | ref }}) graphql.Marshaler {
		{{- end -}}
			{{- if or $type.IsPtrToSlice $type.IsPtrToIntf }}
				{{ if $useFunctionSyntaxForExecutionContext -}}
				return {{ $type.Elem.MarshalFunc }}(ctx, ec, sel, *v)
				{{- else -}}
				return ec.{{ $type.Elem.MarshalFunc }}(ctx, sel, *v)
				{{- end }}
			{{- else if $type.IsSlice }}
				{{- if not $type.GQL.NonNull }}
					if v == nil {
						return graphql.Null
					}
				{{- end }}
				ret := make(graphql.Array, len(v))
				{{- if not $type.IsScalar }}
					var wg sync.WaitGroup
					{{- if gt $.Config.Exec.WorkerLimit 0 }}
						sm := semaphore.NewWeighted({{ $.Config.Exec.WorkerLimit }})
					{{- end }}
					isLen1 := len(v) == 1
					if !isLen1 {
						wg.Add(len(v))
					}
				{{- end }}
				for i := range v {
					{{- if not $type.IsScalar }}
						i := i
						fc := &graphql.FieldContext{
							Index: &i,
							Result: &v[i],
						}
						ctx := graphql.WithFieldContext(ctx, fc)
						f := func(i int) {
							{{- if not $.Config.OmitPanicHandler }}
							defer func() {
								if r := recover(); r != nil {
									ec.Error(ctx, ec.Recover(ctx, r))
									ret = nil
								}
							}()
							{{- end }}
							if !isLen1 {
								{{- if gt $.Config.Exec.WorkerLimit 0 }}
									defer func(){
										sm.Release(1)
										wg.Done()
									}()
								{{- else }}
									defer wg.Done()
								{{- end }}
							}
							{{ if $useFunctionSyntaxForExecutionContext -}}
							ret[i] = {{ $type.Elem.MarshalFunc }}(ctx, ec, sel, v[i])
							{{- else -}}
							ret[i] = ec.{{ $type.Elem.MarshalFunc }}(ctx, sel, v[i])
							{{- end }}
						}
						if isLen1 {
							f(i)
						} else {
							{{- if gt $.Config.Exec.WorkerLimit 0 }}
								if err := sm.Acquire(ctx, 1); err != nil {
									ec.Error(ctx, ctx.Err())
								} else {
									go f(i)
								}
							{{- else }}
								go f(i)
							{{- end }}
						}
					{{ else }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						ret[i] = {{ $type.Elem.MarshalFunc }}(ctx, ec, sel, v[i])
						{{- else -}}
						ret[i] = ec.{{ $type.Elem.MarshalFunc }}(ctx, sel, v[i])
						{{- end }}
					{{- end }}
				}
				{{ if not $type.IsScalar }} wg.Wait() {{ end }}
				{{ if $type.Elem.GQL.NonNull }}
					for _, e := range ret {
						if e == graphql.Null {
							return graphql.Null
						}
					}
				{{ end }}
				return ret
			{{- else if and $type.IsPtrToPtr (not $type.Unmarshaler) (not $type.IsMarshaler) }}
				if v == nil {
					return graphql.Null
				}
				{{ if $useFunctionSyntaxForExecutionContext -}}
				return {{ $type.Elem.MarshalFunc }}(ctx, ec, sel, *v)
				{{- else -}}
				return ec.{{ $type.Elem.MarshalFunc }}(ctx, sel, *v)
				{{- end }}
			{{- else }}
				{{- if $type.IsNilable }}
					if v == nil {
						{{- if $type.GQL.NonNull }}
							if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
								ec.Errorf(ctx, "the requested element is null which the schema does not allow")
							}
						{{- end }}
						return graphql.Null
					}
				{{- end }}
				{{- if $type.IsMarshaler }}
					{{- if $type.IsContext }}
						return graphql.WrapContextMarshaler(ctx, v)
					{{- else }}
						return v
					{{- end }}
				{{- else if $type.Marshaler }}
					{{- $v := "v" }}
					{{- if and $type.IsTargetNilable (not $type.IsNilable) }}
						{{- $v = "&v" }}
					{{- else if and (not $type.IsTargetNilable) $type.IsNilable }}
						{{- $v = "*v" }}
					{{- end }}
					{{- if $type.HasEnumValues }}
						{{- $v = printf "%v[%v]" $type.MarshalFunc $v }}
					{{- else if $type.CastType }}
						{{- $v = printf "%v(%v)" ($type.CastType | ref) $v}}
					{{- end }}
					res := {{ $type.Marshaler | call }}({{ $v }})
					{{- if $type.GQL.NonNull }}
						if res == graphql.Null {
							if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
								ec.Errorf(ctx, "the requested element is null which the schema does not allow")
							}
						}
					{{- end }}
					{{- if $type.IsContext }}
						return graphql.WrapContextMarshaler(ctx, res)
					{{- else }}
						return res
					{{- end }}
				{{- else if $type.IsRoot }}
					{{- if eq $type.Definition.Name "Subscription" }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						res := _{{$type.Definition.Name}}(ctx, ec, sel)
						{{- else -}}
						res := ec._{{$type.Definition.Name}}(ctx, sel)
						{{- end }}
						return res(ctx)
					{{- else }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						return _{{$type.Definition.Name}}(ctx, ec, sel)
						{{- else -}}
						return ec._{{$type.Definition.Name}}(ctx, sel)
						{{- end }}
					{{- end }}
				{{- else }}
					{{ if $useFunctionSyntaxForExecutionContext -}}
					return _{{$type.Definition.Name}}(ctx, ec, sel, {{ if not $type.IsNilable}}&{{end}} v)
					{{- else -}}
					return ec._{{$type.Definition.Name}}(ctx, sel, {{ if not $type.IsNilable}}&{{end}} v)
					{{- end }}
				{{- end }}
			{{- end }}
		}
	{{- end }}

	{{- if $type.HasEnumValues }}
	{{- $enum := $type.GO }}
	{{- if $type.IsNilable }}
		{{- $enum = $type.GO.Elem }}
	{{- end }}
	var (
		{{ $type.UnmarshalFunc }} = map[string]{{ $enum | ref }}{
		{{- range $value := $type.EnumValues }}
			"{{ $value.Definition.Name }}": {{ $value.Object | obj }},
		{{- end }}
		}
		{{ $type.MarshalFunc }} = map[{{ $enum | ref }}]string{
		{{- range $value := $type.EnumValues }}
			 {{ $value.Object | obj }}: "{{ $value.Definition.Name }}",
		{{- end }}
		}
	 )
	{{- end }}
{{- end }}
