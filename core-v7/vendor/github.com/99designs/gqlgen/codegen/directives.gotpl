{{ $useFunctionSyntaxForExecutionContext := .Config.UseFunctionSyntaxForExecutionContext }}

{{ define "implDirectives" }}
	{{ $in := .Field.DirectiveObjName }}
	{{ $useFunctionSyntaxForExecutionContext := .UseFunctionSyntaxForExecutionContext }}
	{{ $zeroVal := .Field.TypeReference.GO | ref}}
	{{- range $i, $directive := .Field.ImplDirectives -}}
		directive{{add $i 1}} := func(ctx context.Context) (any, error) {
			{{- range $arg := $directive.Args }}
				{{- if notNil "Value" $arg }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						{{ $arg.VarName }}, err := {{ $arg.TypeReference.UnmarshalFunc }}(ctx, ec, {{ $arg.Value | dump }})
						{{- else -}}
						{{ $arg.VarName }}, err := ec.{{ $arg.TypeReference.UnmarshalFunc }}(ctx, {{ $arg.Value | dump }})
						{{- end }}
						if err != nil{
							var zeroVal {{$zeroVal}}
							return zeroVal, err
						}
					{{- else if notNil "Default" $arg }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						{{ $arg.VarName }}, err := {{ $arg.TypeReference.UnmarshalFunc }}(ctx, ec, {{ $arg.Default | dump }})
						{{- else -}}
						{{ $arg.VarName }}, err := ec.{{ $arg.TypeReference.UnmarshalFunc }}(ctx, {{ $arg.Default | dump }})
						{{- end }}
						if err != nil{
							var zeroVal {{$zeroVal}}
							return zeroVal, err
						}
					{{- end }}
			{{- end }}
			{{- if not $directive.IsBuiltIn}}
				if {{$directive.CallPath}} == nil {
					var zeroVal {{$zeroVal}}
					return zeroVal, errors.New("directive {{$directive.Name}} is not implemented")
				}
			{{- end}}
			return {{$directive.CallPath}}({{$directive.ResolveArgs $in $i }})
		}
	{{ end -}}
{{ end }}

{{define "queryDirectives"}}
	{{ $useFunctionSyntaxForExecutionContext := .UseFunctionSyntaxForExecutionContext }}
	for _, d := range obj.Directives {
		switch d.Name {
		{{- range $directive := .DirectiveList }}
		case "{{$directive.Name}}":
			{{- if $directive.Args }}
				rawArgs := d.ArgumentMap(ec.Variables)
				{{ if $useFunctionSyntaxForExecutionContext -}}
				args, err := {{ $directive.ArgsFunc }}(ctx,ec,rawArgs)
				{{- else -}}
				args, err := ec.{{ $directive.ArgsFunc }}(ctx,rawArgs)
				{{- end }}
				if err != nil {
					ec.Error(ctx, err)
					return graphql.Null
				}
			{{- end }}
			n := next
			next = func(ctx context.Context) (any, error) {
				{{- template "callDirective" $directive -}}
			}
		{{- end }}
		}
	}
	tmp, err := next(ctx)
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if data, ok := tmp.(graphql.Marshaler); ok {
		return data
	}
	ec.Errorf(ctx, `unexpected type %T from directive, should be graphql.Marshaler`, tmp)
	return graphql.Null
{{end}}

{{define "callDirective"}}
	{{- if not .IsBuiltIn}}
		if {{.CallPath}} == nil {
			return nil, errors.New("directive {{.Name}} is not implemented")
		}
	{{- end}}
	return {{.CallPath}}({{.CallArgs}})
{{end}}

{{ if .Directives.LocationDirectives "QUERY" }}
{{ if $useFunctionSyntaxForExecutionContext -}}
func _queryMiddleware(ctx context.Context, ec *executionContext, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) graphql.Marshaler {
{{- else -}}
func (ec *executionContext) _queryMiddleware(ctx context.Context, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) graphql.Marshaler {
{{- end }}
	{{ template "queryDirectives" (dict "DirectiveList" (.Directives.LocationDirectives "QUERY") "UseFunctionSyntaxForExecutionContext" $useFunctionSyntaxForExecutionContext) }}
}
{{ end }}

{{ if .Directives.LocationDirectives "MUTATION" }}
{{ if $useFunctionSyntaxForExecutionContext -}}
func _mutationMiddleware(ctx context.Context, ec *executionContext, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) graphql.Marshaler {
{{- else -}}
func (ec *executionContext) _mutationMiddleware(ctx context.Context, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) graphql.Marshaler {
{{- end }}
	{{ template "queryDirectives" (dict "DirectiveList" (.Directives.LocationDirectives "MUTATION") "UseFunctionSyntaxForExecutionContext" $useFunctionSyntaxForExecutionContext) }}
}
{{ end }}

{{ if .Directives.LocationDirectives "SUBSCRIPTION" }}
{{ if $useFunctionSyntaxForExecutionContext -}}
func _subscriptionMiddleware(ctx context.Context, ec *executionContext, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) func(ctx context.Context) graphql.Marshaler {
{{- else -}}
func (ec *executionContext) _subscriptionMiddleware(ctx context.Context, obj *ast.OperationDefinition, next func(ctx context.Context) (any, error)) func(ctx context.Context) graphql.Marshaler {
{{- end }}
	for _, d := range obj.Directives {
		switch d.Name {
		{{- range $directive := .Directives.LocationDirectives "SUBSCRIPTION" }}
		case "{{$directive.Name}}":
			{{- if $directive.Args }}
				rawArgs := d.ArgumentMap(ec.Variables)
				{{ if $useFunctionSyntaxForExecutionContext -}}
				args, err := {{ $directive.ArgsFunc }}(ctx,ec,rawArgs)
				{{- else -}}
				args, err := ec.{{ $directive.ArgsFunc }}(ctx,rawArgs)
				{{- end }}
				if err != nil {
					ec.Error(ctx, err)
					return func(ctx context.Context) graphql.Marshaler {
						return graphql.Null
					}
				}
			{{- end }}
			n := next
			next = func(ctx context.Context) (any, error) {
				{{- template "callDirective" $directive -}}
			}
		{{- end }}
		}
	}
	tmp, err := next(ctx)
	if err != nil {
		ec.Error(ctx, err)
		return func(ctx context.Context) graphql.Marshaler {
			return graphql.Null
		}
	}
	if data, ok := tmp.(func(ctx context.Context) graphql.Marshaler); ok {
		return data
	}
	ec.Errorf(ctx, `unexpected type %T from directive, should be graphql.Marshaler`, tmp)
	return func(ctx context.Context) graphql.Marshaler {
		return graphql.Null
	}
}
{{ end }}

{{ if .Directives.LocationDirectives "FIELD" }}
	{{ if $useFunctionSyntaxForExecutionContext -}}
	func _fieldMiddleware(ctx context.Context, ec *executionContext, obj any, next graphql.Resolver) any {
	{{- else -}}
	func (ec *executionContext) _fieldMiddleware(ctx context.Context, obj any, next graphql.Resolver) any {
	{{- end }}
		{{- if .Directives.LocationDirectives "FIELD" }}
		fc := graphql.GetFieldContext(ctx)
		for _, d := range fc.Field.Directives {
			switch d.Name {
			{{- range $directive := .Directives.LocationDirectives "FIELD" }}
			case "{{$directive.Name}}":
				{{- if $directive.Args }}
					rawArgs := d.ArgumentMap(ec.Variables)
					{{ if $useFunctionSyntaxForExecutionContext -}}
					args, err := {{ $directive.ArgsFunc }}(ctx,ec,rawArgs)
					{{- else -}}
					args, err := ec.{{ $directive.ArgsFunc }}(ctx,rawArgs)
					{{- end }}
					if err != nil {
						ec.Error(ctx, err)
						return nil
					}
				{{- end }}
				n := next
				next = func(ctx context.Context) (any, error) {
					{{- template "callDirective" $directive -}}
				}
			{{- end }}
			}
		}
		{{- end }}
		res, err := ec.ResolverMiddleware(ctx, next)
		if err != nil {
			ec.Error(ctx, err)
			return nil
		}
		return res
	}
{{ end }}
