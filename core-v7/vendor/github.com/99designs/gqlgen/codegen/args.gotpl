{{ $useFunctionSyntaxForExecutionContext := .Config.UseFunctionSyntaxForExecutionContext }}

{{ range $name, $args := .Args }}
{{ if $useFunctionSyntaxForExecutionContext -}}
func {{ $name }}(ctx context.Context, ec *executionContext, rawArgs map[string]any) (map[string]any, error) {
{{- else -}}
func (ec *executionContext) {{ $name }}(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
{{- end }}
	var err error
	args := map[string]any{}

	{{- range $i, $arg := . }}
		{{ if $useFunctionSyntaxForExecutionContext -}}
		arg{{$i}}, err := {{ $name }}{{$arg.Name | go}}(ctx, ec, rawArgs)
		{{- else -}}
		arg{{$i}}, err := ec.{{ $name }}{{$arg.Name | go}}(ctx, rawArgs)
		{{- end }}
		if err != nil {
			return nil, err
		}
		args[{{$arg.Name|quote}}] = arg{{$i}}
	{{- end }}
	return args, nil
}

	{{- range $i, $arg := . }}
		{{ if $useFunctionSyntaxForExecutionContext -}}
		func {{ $name }}{{$arg.Name | go}}(
			ctx context.Context,
			ec *executionContext,
			rawArgs map[string]any,
		) ({{ $arg.TypeReference.GO | ref}}, error) {
		{{- else -}}
		func (ec *executionContext) {{ $name }}{{$arg.Name | go}}(
			ctx context.Context,
			rawArgs map[string]any,
		) ({{ $arg.TypeReference.GO | ref}}, error) {
		{{- end }}
			{{- if not .CallArgumentDirectivesWithNull}}
				{{- /*
				We won't call the directive if the argument is null.
				Set call_argument_directives_with_null to true to call directives
				even if the argument is null.
				*/ -}}
				if _, ok := rawArgs[{{$arg.Name|quote}}]; !ok {
					var zeroVal {{ $arg.TypeReference.GO | ref}}
					return zeroVal, nil
				}
			{{end}}
			ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField({{$arg.Name|quote}}))
			{{- if $arg.ImplDirectives }}
				directive0 := func(ctx context.Context) (any, error) {
					tmp, ok := rawArgs[{{$arg.Name|quote}}]
					if !ok {
						var zeroVal {{ $arg.TypeReference.GO | ref}}
						return zeroVal, nil
					}
					{{ if $useFunctionSyntaxForExecutionContext -}}
					return {{ $arg.TypeReference.UnmarshalFunc }}(ctx, ec, tmp)
					{{- else -}}
					return ec.{{ $arg.TypeReference.UnmarshalFunc }}(ctx, tmp)
					{{- end }}
				}
				{{ template "implDirectives" (dict "Field" $arg "UseFunctionSyntaxForExecutionContext" $useFunctionSyntaxForExecutionContext) }}
				tmp, err := directive{{$arg.ImplDirectives|len}}(ctx)
				if err != nil {
					var zeroVal {{ $arg.TypeReference.GO | ref}}
					return zeroVal, graphql.ErrorOnPath(ctx, err)
				}
				if data, ok := tmp.({{ $arg.TypeReference.GO | ref }}) ; ok {
					return data, nil
				{{- if $arg.TypeReference.IsNilable }}
					} else if tmp == nil {
						var zeroVal {{ $arg.TypeReference.GO | ref}}
						return zeroVal, nil
				{{- end }}
				} else {
					var zeroVal {{ $arg.TypeReference.GO | ref}}
					return zeroVal, graphql.ErrorOnPath(ctx, fmt.Errorf(`unexpected type %T from directive, should be {{ $arg.TypeReference.GO }}`, tmp))
				}
			{{- else }}
				if tmp, ok := rawArgs[{{$arg.Name|quote}}]; ok {
					{{ if $useFunctionSyntaxForExecutionContext -}}
					return {{ $arg.TypeReference.UnmarshalFunc }}(ctx, ec, tmp)
					{{- else -}}
					return ec.{{ $arg.TypeReference.UnmarshalFunc }}(ctx, tmp)
					{{- end }}
				}

				var zeroVal {{ $arg.TypeReference.GO | ref}}
				return zeroVal, nil
			{{- end }}
		}
	{{end}}
{{ end }}
