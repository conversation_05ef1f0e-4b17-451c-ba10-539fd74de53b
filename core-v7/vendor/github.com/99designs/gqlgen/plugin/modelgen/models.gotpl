{{ reserveImport "context"  }}
{{ reserveImport "fmt"  }}
{{ reserveImport "io"  }}
{{ reserveImport "strconv"  }}
{{ reserveImport "time"  }}
{{ reserveImport "sync"  }}
{{ reserveImport "errors"  }}
{{ reserveImport "bytes"  }}

{{ reserveImport "github.com/vektah/gqlparser/v2" }}
{{ reserveImport "github.com/vektah/gqlparser/v2/ast" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/introspection" }}

{{- range $model := .Interfaces }}
	{{ with .Description }} {{.|prefixLines "// "}} {{ end }}
	type {{ goModelName .Name }} interface {
		{{- if not .OmitCheck }}
			{{- range $impl := .Implements }}
				Is{{ goModelName $impl }}()
			{{- end }}
			Is{{ goModelName .Name }}()
		{{- end }}
		{{- range $field := .Fields }}
			{{- with .Description }}
				{{.|prefixLines "// "}}
			{{- end}}
			Get{{ $field.GoName }}() {{ $field.Type | ref }}
		{{- end }}
	}
{{- end }}

{{ range $model := .Models }}
	{{with .Description }} {{.|prefixLines "// "}} {{end}}
	type {{ goModelName .Name }} struct {
		{{- range $field := .Fields }}
			{{- with .Description }}
				{{.|prefixLines "// "}}
			{{- end}}
			{{ $field.GoName }} {{$field.Type | ref}} `{{$field.Tag}}`
		{{- end }}
	}

	{{ range .Implements }}
		func ({{ goModelName $model.Name }}) Is{{ goModelName . }}() {}
		{{- with getInterfaceByName . }}
			{{- range .Fields }}
				{{- with .Description }}
					{{.|prefixLines "// "}}
				{{- end}}
				{{ generateGetter $model . }}
			{{- end }}
		{{- end }}
	{{ end }}
{{- end}}

{{ range $enum := .Enums }}
	{{ with .Description }} {{.|prefixLines "// "}} {{end}}
	type {{ goModelName .Name }} string
	const (
	{{- range $value := .Values}}
		{{- with .Description}}
			{{.|prefixLines "// "}}
		{{- end}}
		{{ goModelName $enum.Name .Name }} {{ goModelName $enum.Name }} = {{ .Name|quote }}
	{{- end }}
	)

	var All{{ goModelName .Name }} = []{{ goModelName .Name }}{
	{{- range $value := .Values}}
		{{ goModelName $enum.Name .Name }},
	{{- end }}
	}

	func (e {{ goModelName .Name }}) IsValid() bool {
		switch e {
		case {{ range $index, $element := .Values}}{{if $index}},{{end}}{{ goModelName $enum.Name $element.Name }}{{end}}:
			return true
		}
		return false
	}

	func (e {{ goModelName .Name }}) String() string {
		return string(e)
	}

	func (e *{{ goModelName .Name }}) UnmarshalGQL(v any) error {
		str, ok := v.(string)
		if !ok {
			return fmt.Errorf("enums must be strings")
		}

		*e = {{ goModelName .Name }}(str)
		if !e.IsValid() {
			return fmt.Errorf("%s is not a valid {{ .Name }}", str)
		}
		return nil
	}

	func (e {{ goModelName .Name }}) MarshalGQL(w io.Writer) {
		fmt.Fprint(w, strconv.Quote(e.String()))
	}

{{- end }}
