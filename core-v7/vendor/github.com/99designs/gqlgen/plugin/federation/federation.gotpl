{{ reserveImport "context"  }}
{{ reserveImport "errors"  }}
{{ reserveImport "fmt"  }}
{{ reserveImport "strings"  }}
{{ reserveImport "sync"  }}

{{ reserveImport "github.com/99designs/gqlgen/plugin/federation/fedruntime" }}
{{ $options := .PackageOptions }}
{{ $usePointers := .UsePointers }}

{{ $useFunctionSyntaxForExecutionContext := .UseFunctionSyntaxForExecutionContext }}

var (
	ErrUnknownType = errors.New("unknown type")
	ErrTypeNotFound = errors.New("type not found")
)

func (ec *executionContext) __resolve__service(ctx context.Context) (fedruntime.Service, error) {
	if ec.DisableIntrospection {
		return fedruntime.Service{}, errors.New("federated introspection disabled")
	}

	var sdl []string

	for _, src := range sources {
		if src.BuiltIn {
			continue
		}
		sdl = append(sdl, src.Input)
	}

	return fedruntime.Service{
		SDL: strings.Join(sdl, "\n"),
	}, nil
}

{{if .Entities}}
func (ec *executionContext) __resolve_entities(ctx context.Context, representations []map[string]any) []fedruntime.Entity {
	list := make([]fedruntime.Entity, len(representations))

	repsMap := ec.buildRepresentationGroups(ctx, representations)

	switch len(repsMap) {
	case 0:
		return list
	case 1:
		for typeName, reps := range repsMap {
			ec.resolveEntityGroup(ctx, typeName, reps, list)
		}
		return list
	default:
		var g sync.WaitGroup
		g.Add(len(repsMap))
		for typeName, reps := range repsMap {
			go func(typeName string, reps []EntityWithIndex) {
				ec.resolveEntityGroup(ctx, typeName, reps, list)
				g.Done()
			}(typeName, reps)
		}
		g.Wait()
		return list
	}
}

type EntityWithIndex struct {
	// The index in the original representation array
	index int
	entity EntityRepresentation
}

// EntityRepresentation is the JSON representation of an entity sent by the Router
// used as the inputs for us to resolve.
//
// We make it a map because we know the top level JSON is always an object.
type EntityRepresentation map[string]any

	// We group entities by typename so that we can parallelize their resolution.
	// This is particularly helpful when there are entity groups in multi mode.
func (ec *executionContext) buildRepresentationGroups(
	ctx context.Context,
	representations []map[string]any,
) map[string][]EntityWithIndex {
	repsMap := make(map[string][]EntityWithIndex)
	for i, rep := range representations {
			typeName, ok := rep["__typename"].(string)
			if !ok {
				// If there is no __typename, we just skip the representation;
				// we just won't be resolving these unknown types.
				ec.Error(ctx, errors.New("__typename must be an existing string"))
				continue
			}

		repsMap[typeName] = append(repsMap[typeName], EntityWithIndex{
			index: i,
			entity: rep,
		})
	}

	return repsMap
}

func (ec *executionContext) resolveEntityGroup(
	ctx context.Context,
	typeName string,
	reps []EntityWithIndex,
	list []fedruntime.Entity,
) {
	if isMulti(typeName) {
		err := ec.resolveManyEntities(ctx, typeName, reps, list)
		if err != nil {
			ec.Error(ctx, err)
		}
	} else {
		// if there are multiple entities to resolve, parallelize (similar to
		// graphql.FieldSet.Dispatch)
		var e sync.WaitGroup
		e.Add(len(reps))
		for i, rep := range reps {
			i, rep := i, rep
			go func(i int, rep EntityWithIndex) {
				entity, err := ec.resolveEntity(ctx, typeName, rep.entity)
				if err != nil {
					ec.Error(ctx, err)
				} else {
					list[rep.index] = entity
				}
				e.Done()
			}(i, rep)
		}
		e.Wait()
	}
}

func isMulti(typeName string) bool {
		switch typeName {
		{{- range .Entities -}}
			{{- if .Resolvers -}}
				{{- if .Multi -}}
			case "{{.Def.Name}}":
				return true
				{{ end }}
			{{- end -}}
		{{- end -}}
		default:
			return false
		}
	}

func (ec *executionContext) resolveEntity(
	ctx context.Context,
	typeName string,
	rep EntityRepresentation,
) (e fedruntime.Entity, err error) {
		// we need to do our own panic handling, because we may be called in a
		// goroutine, where the usual panic handling can't catch us
		defer func () {
			if r := recover(); r != nil {
				err = ec.Recover(ctx, r)
			}
		}()

		switch typeName {
			{{ range $_, $entity := .Entities }}
				{{- if and .Resolvers (not .Multi) -}}
				case "{{.Def.Name}}":
					resolverName, err := entityResolverNameFor{{.Def.Name}}(ctx, rep)
					if err != nil {
					return nil, fmt.Errorf(`finding resolver for Entity "{{.Def.Name}}": %w`, err)
					}
					switch resolverName {
					{{ range $i, $resolver := .Resolvers }}
					case "{{.ResolverName}}":
						{{- range $j, $keyField := .KeyFields }}
						{{ if $useFunctionSyntaxForExecutionContext -}}
						id{{$j}}, err := {{.Type.UnmarshalFunc}}(ctx, ec, rep["{{.Field.Join `"].(map[string]any)["`}}"])
						{{- else -}}
						id{{$j}}, err := ec.{{.Type.UnmarshalFunc}}(ctx, rep["{{.Field.Join `"].(map[string]any)["`}}"])
						{{- end }}
							if err != nil {
							return nil, fmt.Errorf(`unmarshalling param {{$j}} for {{$resolver.ResolverName}}(): %w`, err)
							}
						{{- end}}
						entity, err := ec.resolvers.Entity().{{.ResolverName | go}}(ctx, {{- range $j, $_ := .KeyFields -}} id{{$j}}, {{end}})
						if err != nil {
							return nil, fmt.Errorf(`resolving Entity "{{$entity.Def.Name}}": %w`, err)
						}
						{{- if $options.ComputedRequires }}
							{{/* We don't do anything in this case, computed requires are handled by standard resolvers */}}
						{{- else if and $options.ExplicitRequires $entity.Requires }}
							err = ec.Populate{{$entity.Def.Name}}Requires(ctx, {{- if (not $usePointers) -}}&{{- end -}}entity, rep)
							if err != nil {
								return nil, fmt.Errorf(`populating requires for Entity "{{$entity.Def.Name}}": %w`, err)
							}
						{{- else }}
							{{ range $entity.Requires }}
								{{ if $useFunctionSyntaxForExecutionContext -}}
								entity.{{.Field.JoinGo `.`}}, err = {{.Type.UnmarshalFunc}}(ctx, ec, rep["{{.Field.Join `"].(map[string]any)["`}}"])
								{{- else -}}
								entity.{{.Field.JoinGo `.`}}, err = ec.{{.Type.UnmarshalFunc}}(ctx, rep["{{.Field.Join `"].(map[string]any)["`}}"])
								{{- end }}
								if err != nil {
									return nil, err
								}
							{{- end }}
						{{- end }}
						return entity, nil
					{{- end }}
					}
				{{ end }}
			{{- end }}
		}
	return nil, fmt.Errorf("%w: %s", ErrUnknownType, typeName)
	}

func (ec *executionContext) resolveManyEntities(
	ctx context.Context,
	typeName string,
	reps []EntityWithIndex,
	list []fedruntime.Entity,
) (err error) {
		// we need to do our own panic handling, because we may be called in a
		// goroutine, where the usual panic handling can't catch us
		defer func () {
			if r := recover(); r != nil {
				err = ec.Recover(ctx, r)
			}
		}()

		switch typeName {
			{{ range $_, $entity := .Entities }}
				{{ if and .Resolvers .Multi -}}
				case "{{.Def.Name}}":
				resolverName, err := entityResolverNameFor{{.Def.Name}}(ctx, reps[0].entity)
					if err != nil {
						return fmt.Errorf(`finding resolver for Entity "{{.Def.Name}}": %w`, err)
					}
					switch resolverName {
					{{ range $i, $resolver := .Resolvers }}
					case "{{.ResolverName}}":
					typedReps := make([]*{{.LookupInputType}}, len(reps))

						for i, rep := range reps {
							{{ range $i, $keyField := .KeyFields -}}
							{{ if $useFunctionSyntaxForExecutionContext -}}
							id{{$i}}, err := {{.Type.UnmarshalFunc}}(ctx, ec, rep.entity["{{.Field.Join `"].(map[string]any)["`}}"])
							{{- else -}}
							id{{$i}}, err := ec.{{.Type.UnmarshalFunc}}(ctx, rep.entity["{{.Field.Join `"].(map[string]any)["`}}"])
							{{- end }}
								if err != nil {
									return errors.New(fmt.Sprintf("Field %s undefined in schema.", "{{.Definition.Name}}"))
								}
							{{end}}

						typedReps[i] = &{{.LookupInputType}} {
							{{ range $i, $keyField := .KeyFields -}}
								{{$keyField.Field.ToGo}}: id{{$i}},
							{{end}}
							}
						}

					entities, err := ec.resolvers.Entity().{{.ResolverName | go}}(ctx, typedReps)
						if err != nil {
							return err
						}

						for i, entity := range entities {
							{{- range $entity.Requires }}
								{{ if $useFunctionSyntaxForExecutionContext -}}
								entity.{{.Field.JoinGo `.`}}, err = {{.Type.UnmarshalFunc}}(ctx, ec, reps[i].entity["{{.Field.Join `"].(map[string]any)["`}}"])
								{{- else -}}
								entity.{{.Field.JoinGo `.`}}, err = ec.{{.Type.UnmarshalFunc}}(ctx, reps[i].entity["{{.Field.Join `"].(map[string]any)["`}}"])
								{{- end }}
									if err != nil {
										return err
									}
							{{- end}}
						list[reps[i].index] = entity
						}
						return nil
					{{ end }}
					default:
                    	return fmt.Errorf("unknown resolver: %s", resolverName)
					}
				{{ end }}
			{{- end }}
		default:
			return errors.New("unknown type: "+typeName)
	}
}

{{- /* Make sure the required fields are in the given entity representation and return the name of the proper resolver. */ -}}

{{ range $_, $entity := .Entities }}
	{{- if .Resolvers }}

		func entityResolverNameFor{{$entity.Name}}(ctx context.Context, rep EntityRepresentation) (string, error) {
			// we collect errors because a later entity resolver may work fine
			// when an entity has multiple keys
			entityResolverErrs := []error{}
			{{- range .Resolvers }}
				for {
					var (
						m   EntityRepresentation
						val any
						ok  bool
					)
					_ = val
					// if all of the KeyFields values for this resolver are null,
					// we shouldn't use use it
					allNull := true
					{{- range $_, $keyField := .KeyFields }}
						m = rep
						{{- range $i, $field := .Field }}
							val, ok = m["{{.}}"]
							if !ok {
								entityResolverErrs = append(entityResolverErrs,
									fmt.Errorf("%w due to missing Key Field \"{{.}}\" for {{$entity.Name}}", ErrTypeNotFound))
								break
							}
							{{- if (ne $i $keyField.Field.LastIndex ) }}
								if m, ok = val.(map[string]any); !ok {
									// nested field value is not a map[string]interface so don't use it
									entityResolverErrs = append(entityResolverErrs,
										fmt.Errorf("%w due to nested Key Field \"{{.}}\" value not matching map[string]any for {{$entity.Name}}", ErrTypeNotFound))
									break
								}
							{{- else}}
								if allNull {
									allNull = val == nil
								}
							{{- end}}
						{{- end}}
					{{- end }}
					if allNull {
						entityResolverErrs = append(entityResolverErrs,
							fmt.Errorf("%w due to all null value KeyFields for {{$entity.Name}}", ErrTypeNotFound))
						break
					}
					return "{{.ResolverName}}", nil
				}
			{{- end }}
			return "", fmt.Errorf("%w for {{$entity.Name}} due to %v", ErrTypeNotFound,
				errors.Join(entityResolverErrs...).Error())
		}
	{{- end }}
{{- end }}

{{end}}
