{{ range .ExistingImports }}
{{ if ne .<PERSON><PERSON> "" }}
{{ reserveImport .ImportPath .Alias }}
{{ else }}
{{ reserveImport .ImportPath }}
{{ end }}
{{ end }}

{{ range .Populators -}}
{{ if .Comment -}}
// {{.Comment}}
{{- else -}}
// {{.FuncName}} is the requires populator for the {{.Entity.Def.Name}} entity.
{{- end }}
func (ec *executionContext) {{.FuncName}}(ctx context.Context, entity *{{.Entity.GetTypeInfo}}, reps map[string]any) error {
	{{.Implementation}}
}
{{ end }}

{{ .OriginalSource }}
