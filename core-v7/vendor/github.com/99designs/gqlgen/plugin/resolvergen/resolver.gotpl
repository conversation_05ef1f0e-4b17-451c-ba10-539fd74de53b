{{ reserveImport "context"  }}
{{ reserveImport "fmt"  }}
{{ reserveImport "io"  }}
{{ reserveImport "strconv"  }}
{{ reserveImport "time"  }}
{{ reserveImport "sync"  }}
{{ reserveImport "errors"  }}
{{ reserveImport "bytes"  }}

{{ reserveImport "github.com/vektah/gqlparser/v2" }}
{{ reserveImport "github.com/vektah/gqlparser/v2/ast" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/introspection" }}

{{ .Imports }}

{{ if .HasRoot }}
	type {{.ResolverType}} struct {}
{{ end }}

{{ range $resolver := .Resolvers -}}
	{{ if $resolver.Comment -}}
		{{with $resolver.Comment}}{{.|prefixLines "// "}}{{end}}
	{{- else if not $.OmitTemplateComment -}}
		// {{ $resolver.Field.GoFieldName }} is the resolver for the {{ $resolver.Field.Name }} field.
	{{- end }}
	func (r *{{lcFirst $resolver.Object.Name}}{{ucFirst $.ResolverType}}) {{$resolver.Field.GoFieldName}}{{ with $resolver.PrevDecl }}{{ $resolver.Field.ShortResolverSignature .Type }}{{ else }}{{ $resolver.Field.ShortResolverDeclaration }}{{ end }}{
		{{ $resolver.Implementation }}
	}

{{ end }}

{{ range $object := .Objects -}}
	{{ if not $.OmitTemplateComment -}}
		// {{ucFirst $object.Name}} returns {{ $object.ResolverInterface | ref }} implementation.
	{{- end }}
	func (r *{{$.ResolverType}}) {{ucFirst $object.Name}}() {{ $object.ResolverInterface | ref }} { return &{{lcFirst $object.Name}}{{ucFirst $.ResolverType}}{r} }
{{ end }}

{{ range $object := .Objects -}}
	type {{lcFirst $object.Name}}{{ucFirst $.ResolverType}} struct { *{{$.ResolverType}} }
{{ end }}

{{ if (ne .RemainingSource "") }}
    // !!! WARNING !!!
    // The code below was going to be deleted when updating resolvers. It has been copied here so you have
    // one last chance to move it out of harms way if you want. There are two reasons this happens:
	//  - When renaming or deleting a resolver the old code will be put in here. You can safely delete
	//    it when you're done.
	//  - You have helper methods in this file. Move them out to keep these resolver files clean.
	/*
	{{ .RemainingSource }}
	*/
{{ end }}
