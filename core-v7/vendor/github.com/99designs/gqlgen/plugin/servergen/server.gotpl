{{ reserveImport "context" }}
{{ reserveImport "log" }}
{{ reserveImport "net/http" }}
{{ reserveImport "os" }}
{{ reserveImport "github.com/vektah/gqlparser/v2/ast" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/playground" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/handler" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/handler/extension" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/handler/lru" }}
{{ reserveImport "github.com/99designs/gqlgen/graphql/handler/transport" }}

const defaultPort = "8080"

func main() {
	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}

	srv := handler.New({{ lookupImport .ExecPackageName }}.NewExecutableSchema({{ lookupImport .ExecPackageName}}.Config{Resolvers: &{{ lookupImport .ResolverPackageName}}.Resolver{}}))

	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})

	srv.SetQueryCache(lru.New[*ast.QueryDocument](1000))

	srv.Use(extension.Introspection{})
	srv.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New[string](100),
	})

	http.Handle("/", playground.Handler("GraphQL playground", "/query"))
	http.Handle("/query", srv)

	log.Printf("connect to http://localhost:%s/ for GraphQL playground", port)
	log.Fatal(http.ListenAndServe(":" + port, nil))
}
