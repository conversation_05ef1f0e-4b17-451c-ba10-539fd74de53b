package graphql

import (
	"errors"
	"io"
	"strconv"
	"time"
)

func MarshalTime(t time.Time) Marshaler {
	if t.<PERSON>() {
		return Null
	}

	return WriterFunc(func(w io.Writer) {
		io.WriteString(w, strconv.Quote(t.<PERSON>(time.RFC3339Nano)))
	})
}

func UnmarshalTime(v any) (time.Time, error) {
	if tmpStr, ok := v.(string); ok {
		return time.Parse(time.RFC3339Nano, tmpStr)
	}
	return time.Time{}, errors.New("time should be RFC3339Nano formatted string")
}
