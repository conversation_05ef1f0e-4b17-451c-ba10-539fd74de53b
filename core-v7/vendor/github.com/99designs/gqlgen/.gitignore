/vendor
/docs/public
/docs/.hugo_build.lock
/_examples/chat/node_modules
/integration/node_modules
/integration/schema-fetched.graphql
/_examples/chat/package-lock.json
/_examples/federation/package-lock.json
/_examples/federation/node_modules
/codegen/gen
/gen

/.vscode
.idea/
*.test
*.out
gqlgen
*.exe

node_modules

# generated files
/api/testdata/default/graph/generated.go
/api/testdata/federation2/graph/federation.go
/api/testdata/federation2/graph/generated.go