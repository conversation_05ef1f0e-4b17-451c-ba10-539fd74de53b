run:
  tests: true
  timeout: 5m

linters-settings:
  gocritic:
    enabled-checks:
      - emptyStringTest
      - equalFold
      - httpNoBody
      - nilValReturn
      - paramTypeCombine
      - preferFprint
      - yodaStyleExpr
  govet:
    enable-all: true
    disable:
      - fieldalignment
      - shadow
      - unusedwrite # TODO: fix and enable
  errcheck:
    exclude-functions:
      - (io.Writer).Write
      - io.Copy
      - io.WriteString
  perfsprint:
    int-conversion: false
    err-error: false
    errorf: true
    sprintf1: false
    strconcat: false
  revive:
    enable-all-rules: false
    rules:
      - name: empty-lines
      - name: use-any
      - name: struct-tag
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: error-return
      - name: error-naming
      - name: exported
        disabled: true
      - name: if-return
      - name: increment-decrement
      - name: var-declaration
      - name: package-comments
        disabled: true
      - name: range
      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: superfluous-else
      - name: unused-parameter
        disabled: true
      - name: unreachable-code
      - name: redefines-builtin-id
  testifylint:
    disable-all: true
    enable:
      - blank-import
      - bool-compare
      - compares
      - empty
      - encoded-compare
      - error-is-as
      - error-nil
      - expected-actual
      - float-compare
      - go-require
      - len
      - negative-positive
      - nil-compare
      - require-error
      - useless-assert

linters:
  disable-all: true
  enable:
    - bodyclose
    - copyloopvar
    - dupl
    - dupword
    - errcheck
    - gocritic
    - gofmt
    - goimports
    - gosimple
    - govet
    - ineffassign
    - misspell
    - nakedret
    - nolintlint
    - perfsprint
    - prealloc
    - revive
    - staticcheck
    - testifylint
    - typecheck
    - unconvert
    - unused

issues:
  exclude-dirs:
    - bin
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - dupl
        - errcheck
    # It's autogenerated code.
    - path: codegen/testserver/.*/resolver\.go
      linters:
        - gocritic
    # The interfaces are autogenerated and don't conform to the paramTypeCombine rule
    - path: _examples/federation/products/graph/entity.resolvers.go
      linters:
        - gocritic
    # Disable revive.use-any for backwards compatibility
    - path: graphql/map.go
      text: "use-any: since GO 1.18 'interface{}' can be replaced by 'any'"
    - path: codegen/testserver/followschema/resolver.go
      text: "use-any: since GO 1.18 'interface{}' can be replaced by 'any'"
    - path: codegen/testserver/singlefile/resolver.go
      text: "use-any: since GO 1.18 'interface{}' can be replaced by 'any'"
    - path: codegen/testserver/generated_test.go
      linters:
        - staticcheck
      text: SA1019
    - path: plugin/modelgen/models_test.go
      linters:
        - staticcheck
      text: SA1019
