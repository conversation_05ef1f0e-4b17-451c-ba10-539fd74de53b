package main

import (
	"fmt"
	"time"
)

// Test function untuk memverifikasi logika custom schedule
func testCustomScheduleLogic() {
	// Simulasi data dari db.json
	customDates := []string{
		"2025-07-15",
		"2025-07-16", 
		"2025-07-17",
		"2025-07-18",
		"2025-07-19",
		"2025-07-20",
		"2025-07-21",
	}
	
	// Simulasi waktu saat ini
	location, _ := time.LoadLocation("Asia/Jakarta")
	currentTime := time.Date(2025, 7, 16, 18, 0, 0, 0, location) // 16 Juli 2025, 18:00
	
	// Simulasi start_time original
	startTime := time.Date(2025, 7, 16, 17, 13, 0, 0, location) // 16 Juli 2025, 17:13
	
	fmt.Printf("Current time: %s\n", currentTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Original start time: %s\n", startTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Custom dates: %v\n\n", customDates)
	
	// Implementasi logika yang sama seperti di json.go
	var nextDate *time.Time
	
	for _, dateStr := range customDates {
		customDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			continue
		}

		// Set time to match original start time
		customDateTime := time.Date(
			customDate.Year(), customDate.Month(), customDate.Day(),
			startTime.Hour(), startTime.Minute(), startTime.Second(), 0,
			location,
		)

		fmt.Printf("Checking date: %s", customDateTime.Format("2006-01-02 15:04:05"))
		
		// Check if this date is in the future (after current time)
		if customDateTime.After(currentTime) {
			fmt.Printf(" -> FUTURE DATE")
			if nextDate == nil || customDateTime.Before(*nextDate) {
				nextDate = &customDateTime
				fmt.Printf(" -> NEW NEXT DATE")
			}
		} else {
			fmt.Printf(" -> PAST DATE")
		}
		fmt.Println()
	}
	
	if nextDate == nil {
		fmt.Println("\nResult: No future dates found")
	} else {
		fmt.Printf("\nResult: Next schedule should be: %s\n", nextDate.Format("2006-01-02 15:04:05"))
	}
}

// Test skenario 2: Semua tanggal sudah lewat
func testNoFutureDates() {
	fmt.Println("\n=== Test Skenario 2: Semua tanggal sudah lewat ===")

	customDates := []string{
		"2025-07-10",
		"2025-07-11",
		"2025-07-12",
	}

	location, _ := time.LoadLocation("Asia/Jakarta")
	currentTime := time.Date(2025, 7, 16, 18, 0, 0, 0, location)
	startTime := time.Date(2025, 7, 10, 17, 13, 0, 0, location)

	fmt.Printf("Current time: %s\n", currentTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("Custom dates: %v\n\n", customDates)

	var nextDate *time.Time

	for _, dateStr := range customDates {
		customDate, _ := time.Parse("2006-01-02", dateStr)
		customDateTime := time.Date(
			customDate.Year(), customDate.Month(), customDate.Day(),
			startTime.Hour(), startTime.Minute(), startTime.Second(), 0,
			location,
		)

		fmt.Printf("Checking date: %s", customDateTime.Format("2006-01-02 15:04:05"))

		if customDateTime.After(currentTime) {
			fmt.Printf(" -> FUTURE DATE")
			if nextDate == nil || customDateTime.Before(*nextDate) {
				nextDate = &customDateTime
			}
		} else {
			fmt.Printf(" -> PAST DATE")
		}
		fmt.Println()
	}

	if nextDate == nil {
		fmt.Println("\nResult: No future dates found - Stream should be skipped")
	} else {
		fmt.Printf("\nResult: Next schedule should be: %s\n", nextDate.Format("2006-01-02 15:04:05"))
	}
}

func main() {
	fmt.Println("=== Testing Custom Schedule Logic ===")
	testCustomScheduleLogic()
	testNoFutureDates()
}
