package store

// YouTubeService interface untuk update stream data
type YouTubeService interface {
	UpdateLivestreamFromCore(processID string, title string, startTime string, endTime string) error
}

type Store interface {
	// Load data from the store
	Load() (StoreData, error)

	// Save data to the store
	Store(data StoreData) error

    SetOnUpdate(onUpdate func(StoreData)) // TAMBAHKAN INI

    Start() // <-- TAMBAHKAN INI

    SetYouTubeService(service YouTubeService) // <-- TAMBAHKAN INI

}
