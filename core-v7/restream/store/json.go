// store/json.go
package store

import (
	"bytes"
	gojson "encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/datarhei/core/v16/encoding/json"
	"github.com/datarhei/core/v16/io/fs"
	"github.com/datarhei/core/v16/log"
	// "github.com/datarhei/core/v16/restream/app" // HAPUS IMPORT INI
)

// JSONConfig mendefinisikan konfigurasi untuk JSON store.
type JSONConfig struct {
	Filesystem fs.Filesystem
	Filepath   string
	Logger     log.Logger
}

// --- Struct Helper untuk API OpenAI ---
type OpenAIRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIResponse struct {
	Choices []struct {
		Message Message `json:"message"`
	} `json:"choices"`
	Error struct {
		Message string `json:"message"`
	} `json:"error"`
}

// --- Struct Helper untuk Metadata Lokal ---
type YoutubeMetadataHelper struct {
	UpstreamID struct {
		Name     string `json:"name"`
		Outputs  []any  `json:"outputs"` // Tipe bisa diabaikan karena tidak kita modifikasi langsung
		Settings struct {
			Title               string   `json:"title"`
			StartTime           string   `json:"start_time"`
			EndTime             string   `json:"end_time"`
			RepeatSchedule      string   `json:"repeatSchedule"`
			ManualTitleRotating bool     `json:"manualTitleRotating"`
			ManualTitles        string   `json:"manualTitles"`
			AutoGenerateTitle   bool     `json:"autoGenerateTitle"`
			ScheduleEnabled     bool     `json:"schedule_enabled"`
			CustomScheduleDates []string `json:"customScheduleDates"` // Tambahan untuk custom calendar dates
		} `json:"settings"`
	} `json:"upstream-id"`
}

type jsonStore struct {
	fs            fs.Filesystem
	filepath      string
	logger        log.Logger
	lock          sync.RWMutex
	data          StoreData
	openAIKey     string
	httpClient    *http.Client
	onUpdate      func(StoreData) // <-- TAMBAHKAN FIELD INI
	youtubeService YouTubeService // <-- TAMBAHKAN FIELD INI

}

var version uint64 = 4
const timeLayout = "2006-01-02T15:04"

func NewJSON(config JSONConfig, onUpdateCallback func(StoreData)) (Store, error) {
	s := &jsonStore{
		fs:       config.Filesystem,
		filepath: config.Filepath,
		logger:   config.Logger,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	    onUpdate: onUpdateCallback, // Simpan callback yang diberikan

	}

	if len(s.filepath) == 0 {
		s.filepath = "/db.json"
	}
	if s.fs == nil {
		return nil, fmt.Errorf("no valid filesystem provided")
	}
	if s.logger == nil {
		s.logger = log.New("")
	}

	s.openAIKey = os.Getenv("OPENAI_API_KEY_TITLE")
	if s.openAIKey == "" {
		s.logger.Warn().Log("OPENAI_API_KEY environment variable not set. Auto-generate title feature will be disabled.")
	} else {
		s.logger.Info().Log("OpenAI API key loaded successfully.")

		// --- PANGGIL CALLBACK DI SINI! ---
            if s.onUpdate != nil {
                s.logger.Info().Log("Executing onUpdate callback...")
                // Kirim salinan data yang aman untuk dibaca oleh goroutine lain
                s.onUpdate(s.data)
            }
        
	}

	

	data, err := s.load(s.filepath, version)
	if err != nil {
		s.logger.WithError(err).Error().Log("Failed to load initial data from file")
	}
	s.data = data
	s.data.sanitize()

	// go s.PeriodicRefresh()
	// go s.PeriodicScheduleUpdate()

	return s, nil
}

// TAMBAHKAN METODE BARU INI
func (s *jsonStore) Start() {
    s.logger.Info().Log("Starting store background schedulers...")
    go s.PeriodicRefresh()
	go s.PeriodicScheduleUpdate()
}

// Method untuk set YouTube service
func (s *jsonStore) SetYouTubeService(service YouTubeService) {
	s.youtubeService = service
}

// TAMBAHKAN FUNGSI INI
func (s *jsonStore) SetOnUpdate(onUpdate func(StoreData)) {
    s.lock.Lock()
    defer s.lock.Unlock()
    s.onUpdate = onUpdate
}

func (s *jsonStore) generateTitleWithOpenAI(baseTitle string) (string, error) {
	if s.openAIKey == "" {
		return "", fmt.Errorf("OpenAI API key is not configured")
	}

	prompt := fmt.Sprintf("Buat sebuah judul video YouTube yang baru, menarik, dan SEO-friendly dengan tema utama: \"%s\". Jawab hanya dengan judulnya saja, tanpa teks tambahan.", baseTitle)

	requestBody := OpenAIRequest{
		Model: "gpt-3.5-turbo",
		Messages: []Message{
			{Role: "system", Content: "Anda adalah asisten kreatif yang ahli dalam membuat judul YouTube."},
			{Role: "user", Content: prompt},
		},
	}

	jsonData, err := gojson.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal OpenAI request: %w", err)
	}

	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create OpenAI request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.openAIKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request to OpenAI: %w", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read OpenAI response body: %w", err)
	}

	var openAIResp OpenAIResponse
	if err := gojson.Unmarshal(body, &openAIResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal OpenAI response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OpenAI API error (%d): %s", resp.StatusCode, openAIResp.Error.Message)
	}

	if len(openAIResp.Choices) == 0 {
		return "", fmt.Errorf("OpenAI returned no choices")
	}

	newTitle := strings.Trim(openAIResp.Choices[0].Message.Content, " \"\n\r")
	return newTitle, nil
}

func (s *jsonStore) PeriodicScheduleUpdate() {
	time.Sleep(30 * time.Second)
	s.UpdateSchedules()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		s.UpdateSchedules()
	}
}

func (s *jsonStore) UpdateSchedules() {
	var dataForCallback StoreData
	var needsCallback bool = false

	s.lock.Lock()
	currentData := s.data.Clone()
	hasChanged := false
	now := time.Now().UTC()

	location, _ := time.LoadLocation("Asia/Jakarta")

	for id, proc := range currentData.Process {
		if !strings.Contains(id, "egress:youtube") { continue }
		
		procMetaMap, ok := currentData.Metadata.Process[id]
		if !ok { continue }

        // --- AWAL PERUBAHAN UTAMA ---
        // Kita tidak akan unmarshal ke struct helper, tapi bekerja langsung dengan map
		upstreamData, ok := procMetaMap["upstream-id"].(map[string]interface{})
		if !ok { continue }

		settingsMap, ok := upstreamData["settings"].(map[string]interface{})
		if !ok { continue }

        // Ambil nilai dari map, dengan pengecekan tipe data
        // Validasi dan auto-fix untuk schedule_enabled
        scheduleEnabled, exists := settingsMap["schedule_enabled"].(bool)
        if !exists {
            // Jika field schedule_enabled tidak ada, coba deteksi dari kondisi lain
            startTimeStr, hasStart := settingsMap["start_time"].(string)
            endTimeStr, hasEnd := settingsMap["end_time"].(string)
            repeatSchedule, hasRepeat := settingsMap["repeatSchedule"].(string)

            // Jika ada start_time, end_time, dan repeatSchedule yang bukan "off", anggap schedule enabled
            if hasStart && hasEnd && hasRepeat && startTimeStr != "" && endTimeStr != "" && repeatSchedule != "off" {
                scheduleEnabled = true
                settingsMap["schedule_enabled"] = true // Set default value
                s.logger.Info().WithField("id", id).Log("Auto-enabled schedule based on existing time settings")
            } else {
                scheduleEnabled = false
            }
        }

        repeatSchedule, _ := settingsMap["repeatSchedule"].(string)
        endTimeStr, _ := settingsMap["endTime"].(string) // Perhatikan nama field dari JSON asli
        if endTimeStr == "" {
             endTimeStr, _ = settingsMap["end_time"].(string) // Coba nama lain jika perlu
        }

		if !scheduleEnabled || repeatSchedule == "off" {
			s.logger.Info().WithField("id", id).WithField("schedule_enabled", scheduleEnabled).WithField("repeat_schedule", repeatSchedule).Log("Skipping stream - schedule disabled or off")
			continue
		}

		s.logger.Info().WithField("id", id).WithField("repeat_schedule", repeatSchedule).Log("Processing stream for schedule update")

		// Gunakan data dari settings (db.json) untuk grace period check, bukan dari stream_data
		settingsEndTimeStr, _ := settingsMap["end_time"].(string)
		if settingsEndTimeStr == "" {
			settingsEndTimeStr = endTimeStr // fallback ke stream_data jika tidak ada
		}

		settingsEndTime, err := time.ParseInLocation(timeLayout, settingsEndTimeStr, location)
		if err != nil {
			s.logger.Warn().WithField("id", id).WithField("end_time", settingsEndTimeStr).Log("Failed to parse settings end_time")
			continue
		}
		settingsEndTime = settingsEndTime.UTC()

        gracePeriodEndTime := settingsEndTime.Add(1 * time.Minute)
		isGracePeriodPassed := now.After(gracePeriodEndTime)
		isProcessStopped := (proc.Order == "stop" || proc.Order == "")

		s.logger.Info().WithField("id", id).WithField("settings_end_time", settingsEndTimeStr).WithField("grace_period_end", gracePeriodEndTime.Format("2006-01-02 15:04:05")).WithField("current_time", now.Format("2006-01-02 15:04:05")).WithField("grace_passed", isGracePeriodPassed).WithField("process_stopped", isProcessStopped).Log("Checking rescheduling conditions")

		if isGracePeriodPassed && isProcessStopped {
			s.logger.Info().WithField("id", id).Log("Rescheduling condition met - proceeding with update")

            // Ambil data lain yang diperlukan dari map
            startTimeStr, _ := settingsMap["start_time"].(string)
			startTime, err := time.ParseInLocation(timeLayout, startTimeStr, location)
			if err != nil {
				s.logger.Warn().WithField("id", id).WithField("start_time", startTimeStr).Log("Failed to parse start_time")
				continue
			}
			duration := settingsEndTime.Sub(startTime.UTC())

			var newStartTime time.Time
			switch repeatSchedule {
			case "daily": newStartTime = startTime.AddDate(0, 0, 1)
			case "weekly": newStartTime = startTime.AddDate(0, 0, 7)
			case "monthly": newStartTime = startTime.AddDate(0, 1, 0)
			case "yearly": newStartTime = startTime.AddDate(1, 0, 0)
			case "custom":
				// Handle custom schedule dates
				s.logger.Info().WithField("id", id).Log("Processing custom schedule mode")

				customDatesInterface, exists := settingsMap["customScheduleDates"]
				if !exists {
					s.logger.Warn().WithField("id", id).Log("No customScheduleDates found for custom mode - this is required!")
					continue
				}

				customDatesSlice, ok := customDatesInterface.([]interface{})
				if !ok {
					s.logger.Info().WithField("id", id).Log("customScheduleDates is not a slice, skipping")
					continue
				}

				// Convert to string slice
				var customDates []string
				for _, dateInterface := range customDatesSlice {
					if dateStr, ok := dateInterface.(string); ok {
						customDates = append(customDates, dateStr)
					}
				}

				if len(customDates) == 0 {
					s.logger.Info().WithField("id", id).Log("No valid custom dates found, skipping")
					continue
				}

				s.logger.Info().WithField("id", id).WithField("count", len(customDates)).WithField("dates", customDates).Log("Found custom dates")

				// Find next date after current end time
				var nextDate *time.Time
				currentTime := time.Now().In(location)

				s.logger.Info().WithField("id", id).WithField("current_time", currentTime.Format("2006-01-02 15:04:05")).WithField("settings_end_time", settingsEndTime.Format("2006-01-02 15:04:05")).Log("Checking times for custom schedule")

				for _, dateStr := range customDates {
					customDate, err := time.Parse("2006-01-02", dateStr)
					if err != nil {
						s.logger.Warn().WithField("id", id).WithField("date", dateStr).Log("Failed to parse custom date")
						continue
					}

					// Set time to match original start time
					customDateTime := time.Date(
						customDate.Year(), customDate.Month(), customDate.Day(),
						startTime.Hour(), startTime.Minute(), startTime.Second(), 0,
						location,
					)

					s.logger.Info().WithField("id", id).WithField("custom_date", customDateTime.Format("2006-01-02 15:04:05")).Log("Checking custom date")

					// Check if this date is in the future (after current time)
					// This ensures we only schedule for future dates from the custom list
					if customDateTime.After(currentTime) {
						s.logger.Info().WithField("id", id).WithField("date", customDateTime.Format("2006-01-02 15:04:05")).Log("Date is in the future")
						if nextDate == nil || customDateTime.Before(*nextDate) {
							nextDate = &customDateTime
							s.logger.Info().WithField("id", id).WithField("next_date", customDateTime.Format("2006-01-02 15:04:05")).Log("Set as next date")
						}
					} else {
						s.logger.Info().WithField("id", id).WithField("date", customDateTime.Format("2006-01-02 15:04:05")).Log("Date is in the past, skipping")
					}
				}

				if nextDate == nil {
					// No future dates found, skip this stream
					s.logger.Info().WithField("id", id).Log("No future custom dates found, skipping stream")
					continue
				}

				s.logger.Info().WithField("id", id).WithField("selected_date", nextDate.Format("2006-01-02 15:04:05")).Log("Selected next custom date")
				newStartTime = *nextDate
			default: continue
			}

			newEndTime := newStartTime.Add(duration)
			newStartTimeStr := newStartTime.In(location).Format(timeLayout)
			newEndTimeStr := newEndTime.In(location).Format(timeLayout)
			
			// Logika update judul
			currentTitle, _ := settingsMap["title"].(string)
			newTitle := currentTitle
            manualTitles, _ := settingsMap["manualTitles"].(string)
            manualTitleRotating, _ := settingsMap["manualTitleRotating"].(bool)
            autoGenerateTitle, _ := settingsMap["autoGenerateTitle"].(bool)

			if manualTitleRotating && manualTitles != "" {
				// Manual Title Rotating Logic
				s.logger.Info().WithField("id", id).Log("Processing manual title rotating")

				// Split manual titles by newline
				titleList := strings.Split(strings.TrimSpace(manualTitles), "\n")
				if len(titleList) > 0 {
					// Get current title index from metadata or default to 0
					currentTitleIndex := 0
					if titleIndexInterface, exists := settingsMap["currentTitleIndex"]; exists {
						if idx, ok := titleIndexInterface.(float64); ok {
							currentTitleIndex = int(idx)
						}
					}

					// Rotate to next title
					nextTitleIndex := (currentTitleIndex + 1) % len(titleList)
					newTitle = strings.TrimSpace(titleList[nextTitleIndex])

					// Update title index in settings for next rotation
					settingsMap["currentTitleIndex"] = nextTitleIndex

					s.logger.Info().WithField("id", id).WithField("old_title", currentTitle).WithField("new_title", newTitle).WithField("title_index", nextTitleIndex).Log("Manual title rotated")
				}
			} else if autoGenerateTitle {
				// Auto Generate Title with OpenAI
				s.logger.Info().WithField("id", id).Log("Processing auto generate title with OpenAI")

				if generatedTitle, err := s.generateTitleWithOpenAI(currentTitle); err != nil {
					s.logger.Error().WithField("id", id).WithError(err).Log("Failed to generate title with OpenAI, keeping current title")
					// Keep current title if generation fails
				} else {
					newTitle = generatedTitle
					s.logger.Info().WithField("id", id).WithField("old_title", currentTitle).WithField("new_title", newTitle).Log("Title generated with OpenAI")
				}
			}

			// Check if any updates are needed (time or title changes)
			timeChanged := newStartTimeStr != startTimeStr || newEndTimeStr != endTimeStr
			titleChanged := newTitle != currentTitle

			if timeChanged || titleChanged {
				hasChanged = true
				s.logger.Info().WithField("id", id).WithField("mode", repeatSchedule).WithField("time_changed", timeChanged).WithField("title_changed", titleChanged).Log("Applying updates")

                // ### UPDATE SETTINGS MAP ###
                // Modifikasi map 'settingsMap' secara langsung.
                // Ini akan mempertahankan semua field lain yang tidak kita sentuh.
                settingsMap["start_time"] = newStartTimeStr
                settingsMap["end_time"] = newEndTimeStr
                settingsMap["title"] = newTitle

                // ### UPDATE PROCESS DATA ###
                // Perbarui data di `currentData.Process`
				proc.StartTime = newStartTimeStr
				proc.EndTime = newEndTimeStr
				if proc.Config != nil {
					proc.Config.StartTime = newStartTimeStr
					proc.Config.EndTime = newEndTimeStr
				}
				proc.UpdatedAt = now.Unix()
				currentData.Process[id] = proc

				// ### UPDATE METADATA PROCESS ###
				// Pastikan metadata process juga diupdate dengan settings yang baru
				if currentData.Metadata.Process[id] == nil {
					currentData.Metadata.Process[id] = make(map[string]interface{})
				}
				currentData.Metadata.Process[id]["upstream-id"] = upstreamData

				s.logger.Info().WithField("id", id).WithField("old_start", startTimeStr).WithField("new_start", newStartTimeStr).WithField("old_title", currentTitle).WithField("new_title", newTitle).WithField("mode", repeatSchedule).Log("Updated schedule and title")

				// Update stream_data melalui YouTube service jika tersedia
				if s.youtubeService != nil {
					s.logger.Info().WithField("id", id).Log("Updating stream_data via YouTube service")
					err := s.youtubeService.UpdateLivestreamFromCore(id, newTitle, newStartTimeStr, newEndTimeStr)
					if err != nil {
						s.logger.Error().WithField("id", id).WithError(err).Log("Failed to update stream_data")
					} else {
						s.logger.Info().WithField("id", id).Log("Successfully updated stream_data")
					}
				} else {
					s.logger.Warn().WithField("id", id).Log("YouTube service not available, stream_data not updated")
				}
			}
		}
	}
	// --- AKHIR PERUBAHAN UTAMA ---
	
	if hasChanged {
		if err := s.store(s.filepath, currentData); err != nil {
			s.logger.WithError(err).Error().Log("Failed to store updated data")
		} else {
			s.data = currentData
			dataForCallback = s.data
			needsCallback = true
			s.logger.Info().Log("Schedule changes saved, scheduling callback.")
		}
	}
	
	s.lock.Unlock()

	if needsCallback && s.onUpdate != nil {
		s.onUpdate(dataForCallback)
	}
}

func (s *jsonStore) Load() (StoreData, error) {
	s.lock.RLock()
	defer s.lock.RUnlock()
	return s.data, nil
}

func (s *jsonStore) Store(data StoreData) error {
    // 1. Lakukan semua pekerjaan yang butuh lock
	s.lock.Lock()
	if data.Version != version {
		s.lock.Unlock() // Jangan lupa unlock sebelum return
		return fmt.Errorf("invalid version (have: %d, want: %d)", data.Version, version)
	}
	err := s.store(s.filepath, data)
	if err != nil {
		s.lock.Unlock() // Jangan lupa unlock sebelum return
		return fmt.Errorf("failed to store data: %w", err)
	}
	s.data = data
    
    // Simpan data untuk callback nanti
    dataForCallback := s.data
	
    // 2. LEPASKAN LOCK SEKARANG!
    s.lock.Unlock()

	// 3. Panggil callback setelah lock dilepaskan
	if s.onUpdate != nil {
        s.logger.Info().Log(">>>>>> [STORE] Memanggil onUpdate callback (di luar lock)...")
		s.onUpdate(dataForCallback)
	}

	return nil
}

func (s *jsonStore) store(filepath string, data StoreData) error {
	jsondata, err := gojson.MarshalIndent(&data, "", "    ")
	if err != nil {
		return err
	}
	_, _, err = s.fs.WriteFileSafe(filepath, jsondata)
	if err != nil {
		return err
	}
	s.logger.WithField("file", filepath).Debug().Log("Stored data to file")
	return nil
}

type storeVersion struct {
	Version uint64 `json:"version"`
}

func (s *jsonStore) load(filepath string, version uint64) (StoreData, error) {
	r := NewStoreData()
	_, err := s.fs.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) { return r, nil }
		return r, err
	}
	jsondata, err := s.fs.ReadFile(filepath)
	if err != nil { return r, err }
	var db storeVersion
	if err = gojson.Unmarshal(jsondata, &db); err != nil {
		return r, json.FormatError(jsondata, err)
	}
	if db.Version != version {
		return r, fmt.Errorf("unsupported version of the DB file (want: %d, have: %d)", version, db.Version)
	}
	if err = gojson.Unmarshal(jsondata, &r); err != nil {
		return r, json.FormatError(jsondata, err)
	}
	s.logger.WithField("file", filepath).Debug().Log("Read data from file")
	return r, nil
}

func (s *jsonStore) PeriodicRefresh() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		s.logger.Debug().Log("Refreshing data from file...")
		if err := s.RefreshData(); err != nil {
			s.logger.WithError(err).Error().Log("Failed to refresh data from file")
		} else {
			s.logger.Info().Log("Data refreshed from file successfully.")
		}
	}
}

func (s *jsonStore) RefreshData() error {
    var newData StoreData
    
    // --- Blok 1: Lakukan operasi di dalam lock ---
    s.lock.Lock()
    loadedData, err := s.load(s.filepath, version)
	if err != nil { 
        s.lock.Unlock()
        return err 
    }
	loadedData.sanitize()
	s.data = loadedData
    newData = s.data
    s.lock.Unlock() // --- LEPASKAN LOCK DI SINI ---

    // --- Blok 2: Panggil callback di luar lock ---
	if s.onUpdate != nil {
		s.onUpdate(newData)
	}
	return nil
}

// // Jangan lupa tambahkan metode Clone() jika belum ada
// func (d StoreData) Clone() StoreData {
//     bytes, _ := gojson.Marshal(d)
//     var clonedData StoreData
//     gojson.Unmarshal(bytes, &clonedData)
//     return clonedData
// }

