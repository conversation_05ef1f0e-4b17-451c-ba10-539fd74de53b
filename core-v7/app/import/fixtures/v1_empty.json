{"addresses": {"srcAddress": "", "optionalOutputAddress": ""}, "options": {"rtspTcp": true, "video": {"codec": "copy", "preset": "ultrafast", "bitrate": "4096", "fps": "25", "profile": "auto", "tune": "none"}, "audio": {"codec": "auto", "preset": "silence", "bitrate": "64", "channels": "mono", "sampling": "44100"}, "player": {"autoplay": false, "mute": false, "statistics": false, "color": "#3daa48", "logo": {"image": "", "position": "bottom-right", "link": ""}}, "output": {"type": "rtmp", "rtmp": {}, "hls": {"method": "POST", "time": "2", "listSize": "10", "timeout": "10"}}}, "userActions": {"repeatToLocalNginx": "stop", "repeatToOptionalOutput": "stop"}, "states": {"repeatToLocalNginx": {"type": "disconnected", "message": ""}, "repeatToOptionalOutput": {"type": "disconnected", "message": ""}}}