// http/handler/uploadformgdrive.go
package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/datarhei/core/v16/http/handler/util"
	"github.com/datarhei/core/v16/http/models"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
)

const (
	defaultChunkSize        = 1024 * 1024 * 50 // 50MB default chunk size (Digunakan untuk simulasi progress)
	fileSizeThreshold       = 100 * 1024 * 1024  // 100MB threshold (Digunakan untuk simulasi progress)
	gdriveDataFile          = "data/gdrive_data.json"
	redirectURLCookieName   = "gdrive_redirect_url"
	tempFilePrefix          = "gdrive-temp-"
	reportInterval          = 500 * time.Millisecond
)

var (
	ErrInvalidGDriveURL  = errors.New("invalid Google Drive URL")
	ErrNoGDriveAccount = errors.New("no Google Drive account configured")
)

// GDriveAccount menyimpan informasi akun Google Drive.
type GDriveAccount struct {
	Key         string    `json:"key"`
	AccessToken string    `json:"access_token"`
	RefreshToken string   `json:"refresh_token"`
	Expiry      time.Time `json:"expiry"`
	Name        string    `json:"name"`
	ProfilePic  string    `json:"profile_pic"`
}

// progressWriter melaporkan progress pengunduhan.
// Ini *tidak* didefinisikan di sini jika sudah ada di tempat lain.
type progressWriter struct {
	writer      io.Writer
	total       int64
	current     int64
	uploadID    string
	handler     *UploadHandler // Asumsikan UploadHandler sudah didefinisikan
	lastReport  time.Time
	lock        sync.Mutex
}

// Write mengimplementasikan interface `io.Writer`.
func (pw *progressWriter) Write(p []byte) (int, error) {
	n, err := pw.writer.Write(p)
	if err != nil {
		return n, err
	}

	pw.lock.Lock()
	pw.current += int64(n)
	now := time.Now()
	if now.Sub(pw.lastReport) > reportInterval {
		pw.reportProgress()
		pw.lastReport = now
	}
	pw.lock.Unlock()

	return n, nil
}

// reportProgress menghitung dan melaporkan progress.
func (pw *progressWriter) reportProgress() {
	progress := float64(pw.current) / float64(pw.total) * 100
	pw.handler.setProgress(pw.uploadID, progress) // Asumsikan setProgress sudah didefinisikan di UploadHandler

	fmt.Printf("[INFO] Downloading... Progress: %.2f%%\n", progress)
}


// GetGDriveFileSize endpoint untuk mendapatkan ukuran file dari Google Drive
func (h *UploadHandler) GetGDriveFileSize(c echo.Context) error {
	gdriveURL := c.QueryParam("gdrive_url")
	accountKey := c.QueryParam("account_key")

	if gdriveURL == "" {
		return h.errorResponse(c, http.StatusBadRequest, "Google Drive URL is required", nil) // Asumsikan errorResponse sudah didefinisikan
	}

	if accountKey == "" {
		return h.errorResponse(c, http.StatusBadRequest, "Account key is required", nil) // Asumsikan errorResponse sudah didefinisikan
	}

	fileID, err := h.extractFileID(gdriveURL)
	if err != nil {
		return h.errorResponse(c, http.StatusBadRequest, "Invalid Google Drive URL", err) // Asumsikan errorResponse sudah didefinisikan
	}

	fileSize, err := h.getGDriveFileSizeFromAPI(c.Request().Context(), fileID, accountKey)
	if err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to get file size from Google Drive API", err) // Asumsikan errorResponse sudah didefinisikan
	}

	return c.JSON(http.StatusOK, map[string]int64{"fileSize": fileSize})
}

func (h *UploadHandler) getGDriveFileSizeFromAPI(ctx context.Context, fileID string, accountKey string) (int64, error) {
	log.Printf("[GDRIVE] getGDriveFileSizeFromAPI called with fileID: %s, accountKey: %s", fileID, accountKey)

	account, err := h.getGDriveAccount(accountKey)
	if err != nil {
		log.Printf("[GDRIVE] Failed to get Google Drive account: %v", err)
		return 0, fmt.Errorf("failed to get Google Drive account: %w", err)
	}

	config := &oauth2.Config{
		ClientID:     h.gdriveClientID,
		ClientSecret: h.gdriveClientSecret,
		Endpoint:     google.Endpoint,
		RedirectURL:  os.Getenv("GDRIVE_REDIRECT_URI"),
		Scopes:       []string{drive.DriveFileScope},
	}

	token := &oauth2.Token{
		AccessToken:  account.AccessToken,
		RefreshToken: account.RefreshToken,
		TokenType:    "Bearer",
		Expiry:       account.Expiry,
	}

	// Refresh token jika expired
	if token.Expiry.Before(time.Now()) {
		log.Println("[GDRIVE] Access token expired, attempting refresh...")
		newToken, err := config.TokenSource(ctx, token).Token()
		if err != nil {
			log.Printf("[GDRIVE] Failed to refresh token: %v", err)
			return 0, fmt.Errorf("failed to refresh token: %w", err)
		}
		account.AccessToken = newToken.AccessToken
		account.Expiry = newToken.Expiry

		if err := h.saveGDriveAccount(account); err != nil {
			log.Printf("[GDRIVE] Failed to save refreshed token: %v", err)
		} else {
			log.Println("[GDRIVE] Token refreshed and saved successfully")
		}
	}

	// Buat client Google Drive
	srv, err := drive.NewService(ctx, option.WithTokenSource(config.TokenSource(ctx, token)))
	if err != nil {
		log.Printf("[GDRIVE] Unable to create Drive client: %v", err)
		return 0, fmt.Errorf("unable to retrieve Drive client: %w", err)
	}

	// Ambil metadata file
	f, err := srv.Files.Get(fileID).Fields("size").Do()
	if err != nil {
		log.Printf("[GDRIVE] Unable to retrieve metadata for fileID %s: %v", fileID, err)
		return 0, fmt.Errorf("unable to retrieve file metadata: %w", err)
	}

	log.Printf("[GDRIVE] Retrieved file size: %d bytes for fileID: %s", f.Size, fileID)
	return f.Size, nil
}

func (h *UploadHandler) UploadFromGDrive(c echo.Context) error {
    ctx := c.Request().Context()

    fileId := c.FormValue("fileId")
    fileName := filepath.Clean(c.FormValue("filename"))
    channelID := c.FormValue("channelId")
    uploadID := c.FormValue("uploadId")
    accountKey := c.FormValue("account_key")

    log.Printf("[INFO] Received GDrive upload: fileId=%s, filename=%s, uploadID=%s, accountKey=%s\n",
        fileId, fileName, uploadID, accountKey)

    if fileId == "" || fileName == "" || accountKey == "" {
        return h.errorResponse(c, http.StatusBadRequest, "File ID, filename, and account key are required", nil) // Asumsikan errorResponse sudah didefinisikan
    }
    if strings.Contains(fileName, "/") || strings.Contains(fileName, "\\") {
        return h.errorResponse(c, http.StatusBadRequest, "Invalid filename", errors.New("filename contains invalid characters")) // Asumsikan errorResponse sudah didefinisikan
    }

    if uploadID == "" {
        uploadID = uuid.New().String()
    }

    // Inisialisasi progress ke 0
    h.setProgress(uploadID, 0)  // Asumsikan setProgress sudah didefinisikan

    fileSize, err := h.getGDriveFileSizeFromAPI(ctx, fileId, accountKey)
    if err != nil {
        fmt.Printf("[ERROR] Failed to get file size from Google Drive: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to get file size from Google Drive", err) // Asumsikan errorResponse sudah didefinisikan
    }

    // Tentukan lokasi penyimpanan file sementara
    tempFilePath := filepath.Join(h.uploadDir, uploadID+"-"+fileName)
    tempFile, err := os.Create(tempFilePath)
    if err != nil {
        fmt.Printf("[ERROR] Failed to create temporary file: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to create temporary file", err) // Asumsikan errorResponse sudah didefinisikan
    }

    account, err := h.getGDriveAccount(accountKey)
    if err != nil {
        return h.errorResponse(c, http.StatusBadRequest, "Invalid account key", err) // Asumsikan errorResponse sudah didefinisikan
    }

    config := &oauth2.Config{
        ClientID:     h.gdriveClientID,
        ClientSecret: h.gdriveClientSecret,
        Endpoint:     google.Endpoint,
        RedirectURL:  os.Getenv("GDRIVE_REDIRECT_URI"),
        Scopes:       []string{drive.DriveFileScope},
    }

    token := &oauth2.Token{
        AccessToken:  account.AccessToken,
        RefreshToken: account.RefreshToken,
        TokenType:    "Bearer",
        Expiry:       account.Expiry,
    }

    // Refresh token jika expired
    if token.Expiry.Before(time.Now()) {
        fmt.Println("[INFO] Access token expired, refreshing...")
        newToken, err := config.TokenSource(ctx, token).Token()
        if err != nil {
            fmt.Printf("[ERROR] Failed to refresh token: %v\n", err)
            return h.errorResponse(c, http.StatusInternalServerError, "Failed to refresh token", fmt.Errorf("failed to refresh token: %w", err)) // Asumsikan errorResponse sudah didefinisikan
        }
        account.AccessToken = newToken.AccessToken
        account.Expiry = newToken.Expiry
        account.RefreshToken = newToken.RefreshToken

        if err := h.saveGDriveAccount(account); err != nil {
            fmt.Printf("[ERROR] Failed to save updated token: %v\n", err)
        }
    }

    srv, err := drive.NewService(ctx, option.WithTokenSource(config.TokenSource(ctx, token)))
    if err != nil {
        fmt.Printf("[ERROR] Failed to create Drive service: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to create Drive service", err) // Asumsikan errorResponse sudah didefinisikan
    }

    log.Printf("[DEBUG] Downloading file from Google Drive: fileId=%s, filename=%s", fileId, fileName)
    resp, err := srv.Files.Get(fileId).Download()
    if err != nil {
        fmt.Printf("[ERROR] Failed to download file from Google Drive: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to download file from Google Drive", err) // Asumsikan errorResponse sudah didefinisikan
    }
    defer resp.Body.Close()

     // Buat progress writer
    pw := &progressWriter{
        writer:      tempFile,
        total:       fileSize,
        uploadID:    uploadID,
        handler:     h,
        lastReport:  time.Now(),
    }
    defer tempFile.Close()

    // Salin isi file ke file sementara melalui progress writer
    _, err = io.Copy(pw, resp.Body)
    if err != nil {
        fmt.Printf("[ERROR] Failed to copy file content to temporary file: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to copy file content to temporary file", err) // Asumsikan errorResponse sudah didefinisikan
    }

    // Dapatkan durasi video
    finalFile, err := os.Open(tempFilePath)
    if err != nil {
        fmt.Printf("[ERROR] Failed to open merged file: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to open merged file", err) // Asumsikan errorResponse sudah didefinisikan
    }
    defer finalFile.Close()
    defer func() {
        err := os.Remove(tempFilePath)
        if err != nil {
            fmt.Printf("[WARN] Failed to remove temporary file %s: %v\n", tempFilePath, err)
        }
    }()

    // Get the file size
    fileInfo, err := finalFile.Stat()
    if err != nil {
        fmt.Printf("[ERROR] Failed to get merged file size: %v\n", err)
        return h.errorResponse(c, http.StatusInternalServerError, "Failed to get merged file size", err) // Asumsikan errorResponse sudah didefinisikan
    }
    finalFileSize := fileInfo.Size()

    duration, err := util.GetVideoDuration(finalFile, finalFileSize)
    if err != nil {
        fmt.Printf("[WARN] Failed to get duration: %v\n", err)
        duration = "00:00:00"
    }

	// Tentukan direktori tujuan
	destinationDir := filepath.Join("data", "uploads")

	// Buat direktori tujuan jika belum ada
	if _, err := os.Stat(destinationDir); os.IsNotExist(err) {
		err := os.MkdirAll(destinationDir, 0755) // Pastikan izin yang benar
		if err != nil {
			fmt.Printf("[ERROR] Failed to create destination directory: %v\n", err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to create destination directory", err) // Asumsikan errorResponse sudah didefinisikan
		}
	}

	// Tentukan lokasi file akhir
	finalFilePath := filepath.Join(destinationDir, fileName)

	// Pindahkan file dari lokasi sementara ke lokasi permanen
	err = os.Rename(tempFilePath, finalFilePath)
	if err != nil {
		fmt.Printf("[ERROR] Failed to move file to destination: %v\n", err)
		// Coba salin jika rename gagal (mungkin karena lintas partisi)
		err = copyFile(tempFilePath, finalFilePath)
		if err != nil {
			fmt.Printf("[ERROR] Failed to copy file to destination: %v\n", err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to move file to destination", err) // Asumsikan errorResponse sudah didefinisikan
		}
		err = os.Remove(tempFilePath) // Hapus file sementara setelah disalin
		if err != nil {
			fmt.Printf("[WARN] Failed to remove temporary file after copy: %v\n", err)
		}

	}

    metadata := models.FileMetadata{
        ChannelID: strings.TrimSpace(channelID),
        FileName:  fileName,
        FileSize:  fileSize,
        Duration:  duration,
        UploadID:  uploadID,
        FilePath:  finalFilePath, //perubahan disini
    }
    h.setFileMetadata(uploadID, metadata)

    // Save to metadata.json
    metadataFile := filepath.Join(h.uploadDir, "metadata.json")
    if err := h.saveMetadataToFile(metadata, metadataFile); err != nil {
        fmt.Printf("[WARN] Failed to save metadata: %v\n", err)
    }

	//Set progress menjadi 100 sebelum respon dikirim
	h.setProgress(uploadID, 100) // Asumsikan setProgress sudah didefinisikan

    response := map[string]interface{}{
        "message":   "File downloaded and saved successfully",
        "upload_id": uploadID,
        "metadata":  metadata,
        "progress":  100,
    }
    fmt.Printf("[DEBUG] Response JSON: %+v\n", response)
    return c.JSON(http.StatusOK, response)
}

// setProgress mengatur progress upload dengan aman.
// func (h *UploadHandler) setProgress(uploadID string, progress float64) {
//     h.progressLock.Lock()
//     defer h.progressLock.Unlock()
//     h.progress[uploadID] = progress
//     log.Printf("[DEBUG] Setting progress for uploadID %s to %.2f%%\n", uploadID, progress) // Tambahkan log di sini
// }



func copyFile(src, dst string) error {
	sourceFileStat, err := os.Stat(src)
	if err != nil {
		return err
	}

	if !sourceFileStat.Mode().IsRegular() {
		return fmt.Errorf("%s is not a regular file", src)
	}

	source, err := os.Open(src)
	if err != nil {
		return err
	}
	defer source.Close()

	destination, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destination.Close()
	_, err = io.Copy(destination, source)
	return err
}

// extractFileID
func (h *UploadHandler) extractFileID(gdriveURL string) (string, error) {
	parsedURL, err := url.ParseRequestURI(gdriveURL)
	if err != nil {
		return "", fmt.Errorf("invalid URL: %w", err)
	}

	queryParams := parsedURL.Query()
	if id := queryParams.Get("id"); id != "" {
		return id, nil
	}

	// Parsing dari format https://drive.google.com/file/d/FILEID/view
	parts := strings.Split(parsedURL.Path, "/")
	for i, part := range parts {
		if part == "d" && i+1 < len(parts) {
			return parts[i+1], nil
		}
	}

	return "", ErrInvalidGDriveURL
}

// getGDriveAccount mendapatkan informasi akun Google Drive dari file JSON.
func (h *UploadHandler) getGDriveAccount(key string) (*GDriveAccount, error) {
	accounts, err := h.loadGDriveAccounts()
	if err != nil {
		return nil, err
	}

	for _, account := range accounts {
		if account.Key == key {
			return &account, nil
		}
	}

	return nil, fmt.Errorf("account with key '%s' not found", key)
}

// saveGDriveAccount menyimpan atau memperbarui informasi akun Google Drive ke file JSON.
func (h *UploadHandler) saveGDriveAccount(account *GDriveAccount) error {
	accounts, err := h.loadGDriveAccounts()
	if err != nil && !errors.Is(err, os.ErrNotExist) {
		return err
	}

	// Periksa apakah akun sudah ada
	existing := -1
	for i, acc := range accounts {
		if acc.Key == account.Key {
			existing = i
			break
		}
	}

	if existing != -1 {
		// Perbarui akun yang ada
		accounts[existing] = *account
	} else {
		// Tambahkan akun baru
		accounts = append(accounts, *account)
	}

	return h.saveGDriveAccounts(accounts)
}

// saveGDriveAccounts menyimpan daftar akun Google Drive ke file JSON.
func (h *UploadHandler) saveGDriveAccounts(accounts []GDriveAccount) error {
	data, err := json.MarshalIndent(accounts, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal GDrive accounts: %w", err)
	}

	if err := os.MkdirAll(filepath.Dir(gdriveDataFile), 0755); err != nil {
		return fmt.Errorf("failed to create directory for GDrive data file: %w", err)
	}

	if err := os.WriteFile(gdriveDataFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write GDrive data file: %w", err)
	}

	return nil
}

// loadGDriveAccounts memuat daftar akun Google Drive dari file JSON.
func (h *UploadHandler) loadGDriveAccounts() ([]GDriveAccount, error) {
	_, err := os.Stat(gdriveDataFile)
	if os.IsNotExist(err) {
		// File doesn't exist, return empty slice
		return []GDriveAccount{}, nil
	}

	data, err := os.ReadFile(gdriveDataFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read GDrive data file: %w", err)
	}

	var accounts []GDriveAccount
	if err := json.Unmarshal(data, &accounts); err != nil {
		return nil, fmt.Errorf("failed to unmarshal GDrive data: %w", err)
	}

	return accounts, nil
}

// ListGDriveAccounts mengembalikan daftar akun Google Drive yang tersimpan.
func (h *UploadHandler) ListGDriveAccounts(c echo.Context) error {
	accounts, err := h.loadGDriveAccounts()
	if err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to load Google Drive accounts", err) // Asumsikan errorResponse sudah didefinisikan
	}

	// Prepare the response data
	var response []map[string]interface{}
	for _, account := range accounts {
		response = append(response, map[string]interface{}{
			"key":         account.Key,
			"name":        account.Name,
			"profile_pic": account.ProfilePic,
		})
	}

	return c.JSON(http.StatusOK, response)
}

// DeleteGDriveAccount menghapus akun Google Drive berdasarkan key.
func (h *UploadHandler) DeleteGDriveAccount(c echo.Context) error {
	accountKey := c.Param("key")

	if accountKey == "" {
		return h.errorResponse(c, http.StatusBadRequest, "Account key is required", nil) // Asumsikan errorResponse sudah didefinisikan
	}

	accounts, err := h.loadGDriveAccounts()
	if err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to load Google Drive accounts", err) // Asumsikan errorResponse sudah didefinisikan
	}

	// Cari akun yang akan dihapus
	index := -1
	for i, account := range accounts {
		if account.Key == accountKey {
			index = i
			break
		}
	}

	if index == -1 {
		return h.errorResponse(c, http.StatusNotFound, "Account not found", nil) // Asumsikan errorResponse sudah didefinisikan
	}

	// Hapus akun dari slice
	accounts = append(accounts[:index], accounts[index+1:]...)

	// Simpan perubahan
	if err := h.saveGDriveAccounts(accounts); err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to save Google Drive accounts", err) // Asumsikan errorResponse sudah didefinisikan
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Account deleted successfully"})
}

func (h *UploadHandler) GDriveAuthURL(c echo.Context) error {
    fmt.Println("[DEBUG] GDriveAuthURL handler called")

    // Get redirect URL and state from query parameters
    redirectURL := c.QueryParam("redirect_uri")
    state := c.QueryParam("state")

    if redirectURL == "" {
        return h.errorResponse(c, http.StatusBadRequest, "Missing redirect_uri parameter", nil) // Asumsikan errorResponse sudah didefinisikan
    }

    if state == "" {
        return h.errorResponse(c, http.StatusBadRequest, "Invalid state", errors.New("state does not match HOSTNAME")) // Asumsikan errorResponse sudah didefinisikan
    }

    fmt.Printf("[DEBUG] Received auth request from UI. Redirect URL: %s, State: %s, Backend Hostname: %s\n", redirectURL, state, h.hostname)

    // Validasi state dengan HOSTNAME dari environment variable
    if state != h.hostname {
        fmt.Printf("[WARN] Invalid state: %s, expected: %s\n", state, h.hostname)
        return h.errorResponse(c, http.StatusBadRequest, "Invalid state", errors.New("state does not match HOSTNAME")) // Asumsikan errorResponse sudah didefinisikan
    }

    // Store redirect URL in a cookie
    cookie := new(http.Cookie)
    cookie.Name = redirectURLCookieName
    cookie.Value = redirectURL
    cookie.Path = "/"
    c.SetCookie(cookie)

    scopes := url.QueryEscape(strings.Join([]string{
        drive.DriveFileScope,
        "https://www.googleapis.com/auth/userinfo.profile",
        "https://www.googleapis.com/auth/userinfo.email",
    }, " "))

    authURL := fmt.Sprintf(
        "https://accounts.google.com/o/oauth2/auth?client_id=%s&redirect_uri=%s&response_type=code&scope=%s&access_type=offline&prompt=consent&state=%s",
        h.gdriveClientID,
        os.Getenv("GDRIVE_REDIRECT_URI"), // Use os.Getenv("GDRIVE_REDIRECT_URI") here
        scopes,
        state,
    )

    fmt.Printf("[DEBUG] Constructed Google Auth URL: %s\n", authURL)

    // Redirect to the Google Auth URL
    return c.Redirect(http.StatusFound, authURL)
}

func (h *UploadHandler) GDriveAuthCallback(c echo.Context) error {
    log.Println("GDriveAuthCallback called!")

    code := c.QueryParam("code")
    state := c.QueryParam("state")

    log.Println("Received code:", code)
    log.Println("Received state (hostname):", state)

    if code == "" {
        log.Println("Error: Code not found")
        return c.String(http.StatusBadRequest, "Code not found")
    }

    if state == "" {
        log.Println("Error: State not found")
        return c.String(http.StatusBadRequest, "Code not found")
    }

    // Tukar kode OAuth dengan access token
    resp, err := http.PostForm("https://oauth2.googleapis.com/token", url.Values{
        "code":          {code},
        "client_id":     {h.gdriveClientID},
        "client_secret": {h.gdriveClientSecret},
        "redirect_uri":  {os.Getenv("GDRIVE_REDIRECT_URI")}, // Use os.Getenv("GDRIVE_REDIRECT_URI") here
        "grant_type":    {"authorization_code"},
    })
    if err != nil {
        log.Println("Error requesting token:", err)
        return c.String(http.StatusInternalServerError, "Failed to request token")
    }
    defer resp.Body.Close()

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        log.Println("Error reading response body:", err)
        return c.String(http.StatusInternalServerError, "Failed to read token response")
    }
    log.Println("OAuth token response:", string(body))

    var tokenResponse map[string]interface{}
    if err := json.Unmarshal(body, &tokenResponse); err != nil {
        log.Println("Error decoding token response:", err)
        return c.String(http.StatusInternalServerError, "Failed to parse Google response")
    }

    // Cek error dari response
    if errDesc, exists := tokenResponse["error_description"]; exists {
        log.Println("OAuth Error:", errDesc)
        return c.String(http.StatusUnauthorized, "OAuth token request failed: "+errDesc.(string))
    }

    accessToken, ok1 := tokenResponse["access_token"].(string)
    refreshToken, _ := tokenResponse["refresh_token"].(string) // Bisa kosong
    expiresIn, ok3 := tokenResponse["expires_in"].(float64)

    if !ok1 || !ok3 {
        log.Println("Error: Access token or expires_in missing")
        return c.String(http.StatusInternalServerError, "Invalid token response")
    }

    expiresAt := time.Now().Add(time.Duration(expiresIn) * time.Second)

    // Ambil informasi user dari Google API
    client := &http.Client{}
    req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v3/userinfo", nil)
    if err != nil {
        log.Println("cannot create new request", err)
        return c.String(http.StatusInternalServerError, "cannot create new request")
    }
    req.Header.Set("Authorization", "Bearer "+accessToken)
    res, err := client.Do(req)

    if err != nil {
        log.Println("Error fetching user data:", err)
        return c.String(http.StatusInternalServerError, "Failed to fetch user data")
    }
    defer res.Body.Close()

    body, err = io.ReadAll(res.Body)
    if err != nil {
        log.Println("Error reading response body:", err)
        return c.String(http.StatusInternalServerError, "Failed to read user response")
    }
    log.Println("Google API response:", string(body))

    var userInfo map[string]interface{}
    if err := json.Unmarshal(body, &userInfo); err != nil {
        log.Println("Error decoding Google API response:", err)
        return c.String(http.StatusInternalServerError, "Failed to parse Google response")
    }

    email, ok := userInfo["email"].(string)
    if !ok {
        log.Println("Error: User Email not found or invalid format")
        return c.String(http.StatusInternalServerError, "User Email not found or invalid format")
    }
     name, _ := userInfo["name"].(string)
     picture, _ := userInfo["picture"].(string)

    account := &GDriveAccount{
        Key:         email,
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        Expiry:      expiresAt, // Gunakan expiresAt yang dihitung
         Name:        name,
         ProfilePic:  picture,
    }

    if err := h.saveGDriveAccount(account); err != nil {
        log.Println("Error saving data:", err)
        return c.String(http.StatusInternalServerError, "Failed to save data")
    }

    // Ambil frontendURL dari cookie
    cookie, err := c.Cookie(redirectURLCookieName)
    if err != nil {
        log.Println("Error getting frontendURL from cookie:", err)
        return c.String(http.StatusInternalServerError, "frontendURL not found in cookie")
    }

    frontendURL := cookie.Value

    // Redirect ke URL user
    log.Println("Redirecting user to:", frontendURL)
    return c.Redirect(http.StatusFound, frontendURL)
}

// GetGDriveToken Endpoint untuk mendapatkan access token
func (h *UploadHandler) GetGDriveToken(c echo.Context) error {
    log.Println("GetGDriveToken dipanggil")

    var req struct {
        AccountKey string `json:"accountKey"`
    }

    if err := c.Bind(&req); err != nil {
        log.Printf("Gagal memproses body permintaan: %v, error: %v", c.Request().Body, err) // Logging body permintaan
        return h.errorResponse(c, http.StatusBadRequest, "Invalid request body", err)  // Asumsikan errorResponse sudah didefinisikan
    }

    accountKey := req.AccountKey
    log.Printf("Account key diterima: %s\n", accountKey)

    if accountKey == "" {
        log.Println("Account key kosong")
        return h.errorResponse(c, http.StatusBadRequest, "Account key is required", nil) // Asumsikan errorResponse sudah didefinisikan
    }

    account, err := h.getGDriveAccount(accountKey)
    if err != nil {
        log.Printf("Gagal mendapatkan akun Google Drive: %v\n", err)
        return h.errorResponse(c, http.StatusBadRequest, "Invalid account key", err) // Asumsikan errorResponse sudah didefinisikan
    }

    		config := &oauth2.Config{
			ClientID:     h.gdriveClientID,
			ClientSecret: h.gdriveClientSecret,
			Endpoint:     google.Endpoint,
			RedirectURL:  os.Getenv("GDRIVE_REDIRECT_URI"),
			Scopes:       []string{drive.DriveFileScope},
		}

		token := &oauth2.Token{
			AccessToken:  account.AccessToken,
			RefreshToken: account.RefreshToken,
			TokenType:    "Bearer",
			Expiry:       account.Expiry,
		}

		// Refresh token jika expired
		if token.Expiry.Before(time.Now()) {
			log.Println("[GDRIVE] Access token expired, attempting refresh...")
			newToken, err := config.TokenSource(context.Background(), token).Token()
			if err != nil {
				log.Printf("[GDRIVE] Failed to refresh token: %v", err)
				return h.errorResponse(c, http.StatusInternalServerError, "Failed to refresh token", err) // Asumsikan errorResponse sudah didefinisikan
			}
			account.AccessToken = newToken.AccessToken
			account.Expiry = newToken.Expiry

			if err := h.saveGDriveAccount(account); err != nil {
				log.Printf("[GDRIVE] Failed to save refreshed token: %v", err)
			} else {
				log.Println("[GDRIVE] Token refreshed successfully")
			}
		}

		// Buat respons dengan struktur yang diharapkan
		response := map[string]interface{}{
			"err": nil,
			"val": map[string]string{"accessToken": account.AccessToken},
		}
		log.Printf("Mengirim response: %+v\n", response)
		return c.JSON(http.StatusOK, response)
	}

	func (h *UploadHandler) GetGDrivePickerURL(c echo.Context) error {
		accountKey := c.QueryParam("account_key")

		if accountKey == "" {
			return h.errorResponse(c, http.StatusBadRequest, "Account key is required", nil) // Asumsikan errorResponse sudah didefinisikan
		}

		account, err := h.getGDriveAccount(accountKey)
		if err != nil {
			return h.errorResponse(c, http.StatusBadRequest, "Invalid account key", err) // Asumsikan errorResponse sudah didefinisikan
		}

		config := &oauth2.Config{
			ClientID:     h.gdriveClientID,
			ClientSecret: h.gdriveClientSecret,
			Endpoint:     google.Endpoint,
			RedirectURL:  os.Getenv("GDRIVE_REDIRECT_URI"),
			Scopes:       []string{drive.DriveFileScope},
		}

		token := &oauth2.Token{
			AccessToken:  account.AccessToken,
			RefreshToken: account.RefreshToken,
			TokenType:    "Bearer",
			Expiry:       account.Expiry,
		}

		// Refresh token jika expired
		if token.Expiry.Before(time.Now()) {
			newToken, err := config.TokenSource(context.Background(), token).Token()
			if err != nil {
				return h.errorResponse(c, http.StatusInternalServerError, "Failed to refresh token", err) // Asumsikan errorResponse sudah didefinisikan
			}
			account.AccessToken = newToken.AccessToken
			account.Expiry = newToken.Expiry
			if err := h.saveGDriveAccount(account); err != nil {
				log.Printf("[GDRIVE] Failed to save refreshed token: %v", err)
			}
		}

		authURL := config.AuthCodeURL("state", oauth2.AccessTypeOffline, oauth2.ApprovalForce)

		return c.JSON(http.StatusOK, map[string]string{"authUrl": authURL})
	}

	// GetUploadProgress Endpoint untuk mendapatkan progress upload berdasarkan uploadID.
func (h *UploadHandler) GetUploadProgress(c echo.Context) error {
    uploadID := c.Param("uploadId")

    h.progressLock.RLock()
    progress, ok := h.progress[uploadID]
    h.progressLock.RUnlock()

    if !ok {
        return h.errorResponse(c, http.StatusNotFound, "Upload not found", nil) // Asumsikan errorResponse sudah didefinisikan
    }

    response := map[string]interface{}{
        "progress": progress,
    }
    log.Printf("[DEBUG] GetUploadProgress: uploadID=%s, progress=%.2f%%\n", uploadID, progress) // Tambahkan log di sini
    return c.JSON(http.StatusOK, response)
}