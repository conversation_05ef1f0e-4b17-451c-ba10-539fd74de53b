package handler

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"


	"github.com/joho/godotenv"
	"github.com/labstack/echo/v4"
	_ "github.com/go-sql-driver/mysql"

)

// Struktur data untuk menampung hasil query
type OrderDetail struct {
	ClientID    int    `json:"client_id"`
	FirstName   string `json:"first_name"`
	Email       string `json:"email"`
	Title       string `json:"title"`
	Period      string `json:"period"`
	Price       string `json:"price"`
	SisaDurasi  string `json:"sisa_durasi"` // Ubah ke string untuk deskripsi
	OrderID     int    `json:"order_id"`
	Status      string `json:"status"`
	ExpiresAt   string `json:"expires_at"`
	Config      ClientConfig `json:"config"` // Tambahkan struct Config
}

//ClientConfig struct
type ClientConfig struct {
	YoutubeAPI        bool `json:"YoutubeAPI"`
	YoutubeChatbot    bool `json:"YoutubeChatbot"`
	TokenAPI          int  `json:"TokenAPI"`
	TokenChatboot     int  `json:"TokenChatboot"`
	RTMPserver        bool `json:"RTMPserver"`
	RTMPstreaming     bool `json:"RTMPstreaming"`
	YoutubeStreaming  bool `json:"YoutubeStreaming"`
	FacebookStreaming bool `json:"FacebookStreaming"`
	InstagramStreaming bool `json:"InstagramStreaming"`
}

var Db *sql.DB // Koneksi database global (Ekspor dengan huruf besar)

func init() {
	// Load .env file (optional, hanya jika Anda ingin default di luar Docker)
	err := godotenv.Load()
	if err != nil {
		log.Println("Error loading .env file, menggunakan environment variable yang sudah ada")
	}

	Db = InitDB()
	if Db == nil {
		log.Fatalf("Failed to initialize database connection")
	}
}

// InitDB menginisialisasi koneksi database menggunakan environment variables.
func InitDB() *sql.DB {
	// Ambil dari environment variables, dengan nilai default jika tidak diset
	dbUser := os.Getenv("DB_USER")

	dbPass := os.Getenv("DB_PASS")

	dbHost := os.Getenv("DB_HOST")

	dbPort := os.Getenv("DB_PORT")

	dbName := os.Getenv("DB_NAME")

	dbCharset := os.Getenv("DB_CHARSET")

	// Susun DSN (Data Source Name)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		dbUser, dbPass, dbHost, dbPort, dbName, dbCharset)

	var err error
	Db, err = sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Gagal membuka koneksi ke database: %v", err)
	}

	err = Db.Ping()
	if err != nil {
		log.Fatalf("Gagal melakukan ping ke database: %v", err)
	}

	fmt.Println("✅ Koneksi ke database berhasil!")
	return Db
}

// GetOrderIDFromEnv mendapatkan ID order dari environment variable.
func GetOrderIDFromEnv() int {
	orderIDStr := os.Getenv("ORDER_ID")
	if orderIDStr == "" {
		// Default value jika environment variable tidak diset
		orderIDStr = "87" // Ganti dengan ID default yang sesuai
		fmt.Println("Menggunakan ORDER_ID default:", orderIDStr)
	}

	orderID, err := strconv.Atoi(orderIDStr)
	if err != nil {
		log.Fatalf("Invalid ORDER_ID environment variable: %v", err)
	}
	return orderID
}

//EmailService Interface
type EmailService interface {
	SendConfirmationEmail(email, confirmationURL string) error
}

//SMTPEmailService Implementation
type SMTPEmailService struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
}

// func (s *SMTPEmailService) SendConfirmationEmail(email, confirmationURL string) error {
// 	log.Printf("Mengirim email ke %s dengan tautan konfirmasi %s", email, confirmationURL)

// 	// Log SMTP configuration
// 	log.Printf("SMTP Configuration: Host=%s, Port=%d, Username=%s, From=%s", s.Host, s.Port, s.Username, s.From)

// 	m := gomail.NewMessage()
// 	m.SetHeader("From", s.From)
// 	m.SetHeader("To", email)
// 	m.SetHeader("Subject", "Konfirmasi Reset Password")
// 	m.SetBody("text/plain", fmt.Sprintf("Silakan klik tautan berikut untuk mengonfirmasi pengaturan ulang kata sandi Anda:\n%s", confirmationURL))

// 	smtpPort := s.Port // Ambil port dari struct

// 	d := gomail.NewDialer(s.Host, smtpPort, s.Username, s.Password)

// 	// Konfigurasi TLS (Opsional, tergantung kebutuhan server SMTP)
// 	if smtpPort == 465 {
// 		d.TLSConfig = &tls.Config{InsecureSkipVerify: true} // HAPUS INI DI PRODUKSI! Ganti dengan validasi sertifikat yang benar.
// 		log.Println("Menggunakan TLS dengan InsecureSkipVerify (Port 465) - HANYA UNTUK DEV")
// 	}

// 	if err := d.DialAndSend(m); err != nil {
// 		log.Printf("Failed to send email: %v", err)
// 		return fmt.Errorf("failed to send email: %w", err)
// 	}

// 	log.Println("Email sent successfully")
// 	return nil
// }

type DashboardHandler struct {
	DB *sql.DB
	EmailService EmailService // Add EmailService
}

// NewDashboardHandler membuat instance DashboardHandler dengan koneksi database.
func NewDashboardHandler(db *sql.DB, emailService EmailService) *DashboardHandler {
	return &DashboardHandler{DB: db, EmailService: emailService}
}

// GetData mengambil data order dan live details dan mengirimkannya sebagai JSON.
func (h *DashboardHandler) GetData(c echo.Context) error {
	orderID := GetOrderIDFromEnv()

	// Log request received
	log.Printf("Menerima permintaan untuk mengambil data dengan Order ID: %d dari IP: %s", orderID, c.RealIP())

	orderData, err := h.FetchOrderData(orderID)
	if err != nil {
		log.Printf("Gagal mengambil data order: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Gagal mengambil data order"})
	}
	log.Printf("Data Order yang di Terima: %+v", orderData)

	// Ambil live details dari livedetail.go
	liveDetails, err := GetLiveDetails("config/db.json") // Ganti dengan path yang benar
	if err != nil {
		log.Printf("Gagal menghitung live details: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Gagal menghitung live details"})
	}

	log.Printf("Data live yang di Terima: %+v", liveDetails)
	// Buat respons JSON yang menggabungkan data order dan live details
	response := map[string]interface{}{
		"title":       orderData.Title,
		"period":      orderData.Period,
		"sisa_durasi": orderData.SisaDurasi,
		"first_name":  orderData.FirstName,
		"order_id":    orderData.OrderID,
		"email":       orderData.Email,
		"client_id":   orderData.ClientID,
		"price":       orderData.Price,
		"status":      orderData.Status,
		"expires_at":  orderData.ExpiresAt,
		"totalLive":     liveDetails.TotalLive,
		"liveAktif":     liveDetails.LiveAktif,
		"liveTerjadwal": liveDetails.LiveTerjadwal,
		"liveSelesai":   liveDetails.LiveSelesai,
		"liveSet":		liveDetails.LiveSet,
		"config": orderData.Config, // Tambahkan data config
	}

	log.Printf("Data yang akan dikirim sebagai respons JSON: %+v", response)

	return c.JSON(http.StatusOK, response)
}

func (h *DashboardHandler) FetchOrderData(orderID int) (*OrderDetail, error) {
	log.Printf("Mulai FetchOrderData untuk orderID: %d", orderID)
	query := `
		SELECT
			c.id,
			c.first_name,
			c.email,
			co.title,
			co.period,
			co.price,
			co.status,
			co.expires_at,
			co.config -- Tambahkan kolom config
		FROM client_order co
		LEFT JOIN client c ON co.client_id = c.id
		WHERE co.id = ?
	`

	log.Println("Menjalankan query:", query)
	row := Db.QueryRow(query, orderID)

	var (
		clientID    int
		firstName   string
		email       string
		title       string
		periodStr   string
		price       string
		status      string
		expiresAtStr string
		configStr    string // Menyimpan data JSON config dari database
	)

	log.Println("Memulai scan row")
	err := row.Scan(
		&clientID,
		&firstName,
		&email,
		&title,
		&periodStr,
		&price,
		&status,
		&expiresAtStr,
		&configStr, // Scan data config ke variabel configStr
	)
	log.Println("Selesai scan row")

	if err != nil {
		log.Printf("Error saat scan row: %v", err)
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("Order dengan ID %d tidak ditemukan", orderID)
		}
		return nil, fmt.Errorf("Gagal mengambil data order: %v", err)
	}

	// Tambahkan log ini untuk mencetak data mentah yang diambil dari database
	log.Printf("Data mentah dari database: clientID=%d, firstName=%s, email=%s, title=%s, periodStr=%s, price=%s, status=%s, expiresAtStr=%s, configStr=%s",
		clientID, firstName, email, title, periodStr, price, status, expiresAtStr, configStr)

	expiresAtValue := ""
	var sisaDurasiStr string

	if expiresAtStr != "" {
		expiresAtValue = expiresAtStr
		log.Println("expiresAtStr tidak kosong:", expiresAtStr)

		expiresAt, err := time.Parse(time.RFC3339, expiresAtStr)
		if err != nil {
			log.Printf("Gagal memparse expires_at: %v", err)
			return nil, fmt.Errorf("Gagal memparse expires_at: %v", err)
		}

		wibLoc, err := time.LoadLocation("Asia/Jakarta")
		if err != nil {
			log.Printf("Gagal memuat lokasi waktu WIB: %v", err)
			return nil, fmt.Errorf("Gagal memuat lokasi waktu WIB: %v", err)
		}

		now := time.Now().In(wibLoc)

		sisaDurasi := int(expiresAt.Sub(now).Hours() / 24)

		if sisaDurasi >= 0 {
			sisaDurasiStr = fmt.Sprintf(" %d Hari", sisaDurasi)
		} else {
			sisaDurasiStr = fmt.Sprintf("Lewat %d Hari", -sisaDurasi)
		}
	} else {
		sisaDurasiStr = "Tidak Ada Batas Waktu"
		log.Println("expiresAtStr kosong")
	}

	periodValue := h.formatPeriod(periodStr)
	log.Println("periodValue:", periodValue)

	// Unmarshal JSON config ke struct ClientConfig
	var config ClientConfig
	if configStr != "" {
		err = json.Unmarshal([]byte(configStr), &config)
		if err != nil {
			log.Printf("Gagal unmarshal config JSON: %v", err)
			// Handle error, misalnya dengan memberikan nilai default untuk config
			config = ClientConfig{} // Set ke default jika gagal unmarshal
		}
	} else {
		config = ClientConfig{} // Set ke default jika string kosong
	}

	orderDetail := &OrderDetail{
		ClientID:    clientID,
		FirstName:   firstName,
		Email:       email,
		Title:       title,
		Period:      periodValue,
		Price:       price,
		SisaDurasi:  sisaDurasiStr,
		OrderID:     orderID,
		Status:      status,
		ExpiresAt:   expiresAtValue,
		Config:      config, // Set data config
	}
	log.Printf("Selesai FetchOrderData untuk orderID: %d", orderID)

	return orderDetail, nil
}

// formatPeriod memformat string period
func (h *DashboardHandler) formatPeriod(periodStr string) string {
	periodStr = strings.ToUpper(periodStr)
	if strings.HasSuffix(periodStr, "M") {
		monthsStr := strings.TrimSuffix(periodStr, "M")
		months, err := strconv.Atoi(monthsStr)
		if err != nil {
			return "Invalid Period"
		}

		if months == 1 {
			return "1 Bulan"
		} else {
			return fmt.Sprintf("%d Bulan", months)
		}
	} else if strings.HasSuffix(periodStr, "D") {
		daysStr := strings.TrimSuffix(periodStr, "D")
		days, err := strconv.Atoi(daysStr)
		if err != nil {
			return "Invalid Period"
		}
		return fmt.Sprintf("%d Hari", days)
	} else {
		return "Invalid Period"
	}
}

func saveToJSON(filename string, data interface{}) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("Gagal membuat file: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("Gagal menyimpan data ke JSON: %v", err)
	}

	fmt.Println("✅ Data berhasil disimpan ke", filename)
	return nil
}

// UpdateTokensInDB updates the TokenAPI and TokenChatboot values in the database for a given order ID.
func (h *DashboardHandler) UpdateTokensInDB(orderID int, tokenAPI int, tokenChatboot int) error {
	log.Printf("Memulai UpdateTokensInDB untuk orderID: %d, TokenAPI: %d, TokenChatboot: %d", orderID, tokenAPI, tokenChatboot)

	// 1. Ambil data config yang sudah ada dari database
	orderData, err := h.FetchOrderData(orderID)
	if err != nil {
		return fmt.Errorf("Gagal mengambil data order: %w", err)
	}

	// 2. Modifikasi data config yang sudah ada dengan nilai TokenAPI dan TokenChatboot yang baru
	orderData.Config.TokenAPI = tokenAPI
	orderData.Config.TokenChatboot = tokenChatboot

	// 3. Serialize kembali struct ClientConfig ke string JSON
	configJSON, err := json.Marshal(orderData.Config)
	if err != nil {
		return fmt.Errorf("Gagal marshal config JSON: %w", err)
	}

	// 4. Update kolom config di database dengan string JSON yang baru
	query := `
		UPDATE client_order
		SET config = ?
		WHERE id = ?
	`

	log.Println("Menjalankan query:", query)

	_, err = h.DB.Exec(query, configJSON, orderID)
	if err != nil {
		return fmt.Errorf("Gagal memperbarui token di database: %w", err)
	}

	log.Println("✅ Token berhasil diupdate di database")
	return nil
}

//ResetPassword handles the reset password request
// func (h *DashboardHandler) ResetPassword(c echo.Context) error {
// 	type ResetRequest struct {
// 		Email string `json:"email" validate:"required,email"`
// 	}

// 	req := new(ResetRequest)
// 	if err := c.Bind(req); err != nil {
// 		log.Printf("Binding error: %v", err)
// 		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
// 	}

// 	if err := c.Validate(req); err != nil {
// 		log.Printf("Validation error: %v", err)
// 		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid email format"})
// 	}

// 	// Fetch user email from DB to validate existence
// 	var userEmail string
// 	err := h.DB.QueryRow("SELECT email FROM client WHERE email = ?", req.Email).Scan(&userEmail)
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			log.Printf("Email not found: %s", req.Email)
// 			return c.JSON(http.StatusNotFound, map[string]string{"error": "Email not found"})
// 		}
// 		log.Printf("Database error: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Database error"})
// 	}

// 	// Generate a confirmation token (UUID)
// 	confirmationToken, err := generateConfirmationToken()
// 	if err != nil {
// 		log.Printf("Failed to generate confirmation token: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate confirmation token"})
// 	}

// 	// Store the token in the database (associate with the user's email)
// 	err = h.storeConfirmationToken(req.Email, confirmationToken)
// 	if err != nil {
// 		log.Printf("Failed to store confirmation token: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to store confirmation token"})
// 	}

// 	// Ambil HOSTNAME dari variabel lingkungan
// 	hostname := os.Getenv("HOSTNAME")
// 	if hostname == "" {
// 		log.Println("HOSTNAME environment variable not set")
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "HOSTNAME environment variable not set"})
// 	}

// 	// Buat URL konfirmasi
// 	confirmationURL := fmt.Sprintf("https://live.upstream.id/%s/confirm-reset?token=%s", hostname, confirmationToken)
// 	log.Printf("Confirmation URL: %s", confirmationURL)

// 	// Send confirmation email
// 	if h.EmailService == nil {
// 		log.Println("Email service not initialized")
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Email service not initialized"})
// 	}

// 	if err := h.EmailService.SendConfirmationEmail(req.Email, confirmationURL); err != nil {
// 		log.Printf("Failed to send confirmation email: %v\n", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to send confirmation email"})
// 	}

// 	return c.JSON(http.StatusOK, map[string]string{"message": "Password reset email sent.  Please check your inbox."})
// }

//ConfirmReset handles the confirmation link click
// func (h *DashboardHandler) ConfirmReset(c echo.Context) error {
//     token := c.QueryParam("token")
//     log.Printf("ConfirmReset endpoint hit with token: %s", token)

//     log.Printf("Verifying token: %s", token) // Tambahkan log

//     // Verify the token against the database
//     email, err := h.verifyConfirmationToken(token)
//     if err != nil {
//         log.Printf("Invalid or expired confirmation token: %v", err)
//         return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid or expired confirmation token"})
//     }
//     log.Printf("Confirmation token verified for email: %s", email)

//     // Token is valid, proceed with reset
//     // On confirmation, delete the config.json file
//     configPath := filepath.Join("config", "config.json")
//     err = os.Remove(configPath)
//     if err != nil {
//         if os.IsNotExist(err) {
//             log.Printf("Config file not found: %s", configPath)
//             return c.JSON(http.StatusNotFound, map[string]string{"error": "Config file not found"})
//         }
//         log.Printf("Failed to delete config file: %v", err)
//         return c.JSON(http.StatusInternalServerError, map[string]string{"error": fmt.Sprintf("Failed to delete config file: %v", err)})
//     }
//     log.Printf("Config file deleted: %s", configPath)

//     // Restart the Docker container
//     hostname := os.Getenv("HOSTNAME")
//     if hostname == "" {
//         log.Println("HOSTNAME environment variable not set")
//         return c.JSON(http.StatusInternalServerError, map[string]string{"error": "HOSTNAME environment variable not set"})
//     }

//     // Construct the Docker restart command
//     cmd := exec.Command("docker", "restart", hostname)
//     log.Printf("Restarting Docker container with hostname: %s", hostname)

//     // Run the command
//     output, err := cmd.CombinedOutput()
//     if err != nil {
//         log.Printf("Failed to restart Docker container: %v, Output: %s", err, string(output))
//         return c.JSON(http.StatusInternalServerError, map[string]string{"error": fmt.Sprintf("Failed to restart Docker container: %v", err)})
//     }
//     log.Printf("Docker container restarted successfully, Output: %s", string(output))

//     // Optionally, clear the confirmation token from the database
//     err = h.clearConfirmationToken(email)
//     if err != nil {
//         log.Printf("Failed to clear confirmation token: %v", err)
//         // Log this error, but don't fail the request
//     }
//     log.Println("Confirmation token cleared")

//     return c.JSON(http.StatusOK, map[string]string{"message": "Password reset confirmed, config file deleted, and Docker container restarted."})
// }

// func (h *DashboardHandler) verifyConfirmationToken(token string) (string, error) {
//     // Use prepared statements to prevent SQL injection
//     stmt, err := h.DB.Prepare("SELECT email FROM client WHERE reset_token = ?")
//     if err != nil {
//         log.Printf("Failed to prepare statement: %v", err)
//         return "", err
//     }
//     defer stmt.Close()

//     var email string

//     log.Printf("Executing query with token: %s", token) // Tambahkan log

//     err = stmt.QueryRow(token).Scan(&email)

//     log.Printf("Query result: email=%s, err=%v", email, err) // Tambahkan log

//     if err != nil {
//         if err == sql.ErrNoRows {
//             log.Println("Confirmation token not found")
//         } else {
//             log.Printf("QueryRow scan error: %v", err)
//         }
//         return "", err
//     }
//     log.Printf("Confirmation token verified for email: %s", email)
//     return email, nil
// }

//Helper functions
// func generateConfirmationToken() (string, error) {
// 	token, err := uuid.NewRandom()
// 	if err != nil {
// 		log.Printf("Failed to generate UUID: %v", err)
// 		return "", err
// 	}
// 	log.Printf("Generated confirmation token: %s", token.String())
// 	return token.String(), nil
// }

// func (h *DashboardHandler) storeConfirmationToken(email, token string) error {
// 	// Use prepared statements to prevent SQL injection
// 	stmt, err := h.DB.Prepare("UPDATE client SET reset_token = ? WHERE email = ?")
// 	if err != nil {
// 		log.Printf("Failed to prepare statement: %v", err)
// 		return err
// 	}
// 	defer stmt.Close()

// 	_, err = stmt.Exec(token, email)
// 	if err != nil {
// 		log.Printf("Failed to execute statement: %v", err)
// 		return err
// 	}
// 	log.Printf("Stored confirmation token for email: %s", email)
// 	return nil
// }



// func (h *DashboardHandler) clearConfirmationToken(email string) error {
// 	// Use prepared statements to prevent SQL injection
// 	stmt, err := h.DB.Prepare("UPDATE client SET reset_token = NULL WHERE email = ?")
// 	if err != nil {
// 		log.Printf("Failed to prepare statement: %v", err)
// 		return err
// 	}
// 	defer stmt.Close()

// 	_, err = stmt.Exec(email)
// 	if err != nil {
// 		log.Printf("Failed to execute statement: %v", err)
// 		return err
// 	}
// 	log.Printf("Confirmation token cleared for email: %s", email)
// 	return nil
// }

