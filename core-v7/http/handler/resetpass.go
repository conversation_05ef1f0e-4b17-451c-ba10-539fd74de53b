package handler

import (
	"database/sql"
	
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"crypto/tls"
	"text/template"

	"github.com/labstack/echo/v4"
	_ "github.com/go-sql-driver/mysql"
	"github.com/google/uuid"

	"gopkg.in/gomail.v2"
)

type TemplateData struct {
	RedirectURL string
	ErrorMessage string // Menambahkan pesan kesalahan
}

func (s *SMTPEmailService) SendConfirmationEmail(email, confirmationURL string) error {
	log.Printf("Mengirim email ke %s dengan tautan konfirmasi %s", email, confirmationURL)

	// Log SMTP configuration
	log.Printf("SMTP Configuration: Host=%s, Port=%d, Username=%s, From=%s", s.Host, s.Port, s.Username, s.From)

	// Ambil HOSTNAME dari environment variable
	hostname := os.Getenv("HOSTNAME")
	if hostname == "" {
		log.Println("HOSTNAME environment variable not set, using default value 'localhost'")
		hostname = "localhost" // Atau nilai default lainnya
	}

	m := gomail.NewMessage()
	m.SetHeader("From", s.From)
	m.SetHeader("To", email)
	m.SetHeader("Subject", "Konfirmasi Reset User & Password Panel Live Upstream.ID")
	m.SetBody("text/plain", fmt.Sprintf("Silakan klik tautan berikut untuk mengonfirmasi pengaturan ulang user & password panel streaming %s:\n%s", hostname, confirmationURL))

	smtpPort := s.Port // Ambil port dari struct

	d := gomail.NewDialer(s.Host, smtpPort, s.Username, s.Password)

	// Konfigurasi TLS (Opsional, tergantung kebutuhan server SMTP)
	if smtpPort == 465 {
		d.TLSConfig = &tls.Config{InsecureSkipVerify: true} // HAPUS INI DI PRODUKSI! Ganti dengan validasi sertifikat yang benar.
		log.Println("Menggunakan TLS dengan InsecureSkipVerify (Port 465) - HANYA UNTUK DEV")
	}

	if err := d.DialAndSend(m); err != nil {
		log.Printf("Failed to send email: %v", err)
		return fmt.Errorf("failed to send email: %w", err)
	}

	log.Println("Email sent successfully")
	return nil
}

//Validasi Ke Database
// ResetPassword handles the reset password request
// func (h *DashboardHandler) ResetPassword(c echo.Context) error {
// 	type ResetRequest struct {
// 		Email string `json:"email" validate:"required,email"`
// 	}

// 	req := new(ResetRequest)
// 	if err := c.Bind(req); err != nil {
// 		log.Printf("Binding error: %v", err)
// 		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
// 	}

// 	if err := c.Validate(req); err != nil {
// 		log.Printf("Validation error: %v", err)
// 		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid email format"})
// 	}

// 	// Fetch user email from DB to validate existence
// 	var userEmail string
// 	err := h.DB.QueryRow("SELECT email FROM client WHERE email = ?", req.Email).Scan(&userEmail)
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			log.Printf("Email not found: %s", req.Email)
// 			return c.JSON(http.StatusNotFound, map[string]string{"error": "Email not found"})
// 		}
// 		log.Printf("Database error: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Database error"})
// 	}

// 	// Generate a confirmation token (UUID)
// 	confirmationToken, err := generateConfirmationToken()
// 	if err != nil {
// 		log.Printf("Failed to generate confirmation token: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate confirmation token"})
// 	}

// 	// **Hapus token lama (jika ada) dan simpan token baru**
// 	err = h.replaceConfirmationToken(req.Email, confirmationToken)
// 	if err != nil {
// 		log.Printf("Failed to store confirmation token: %v", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to store confirmation token"})
// 	}

// 	// Ambil HOSTNAME dari variabel lingkungan
// 	hostname := os.Getenv("HOSTNAME")
// 	if hostname == "" {
// 		log.Println("HOSTNAME environment variable not set")
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "HOSTNAME environment variable not set"})
// 	}

// 	// Buat URL konfirmasi
// 	confirmationURL := fmt.Sprintf("https://live.upstream.id/%s/confirm-reset?token=%s", hostname, confirmationToken)
// 	log.Printf("Confirmation URL: %s", confirmationURL)

// 	// Send confirmation email
// 	if h.EmailService == nil {
// 		log.Println("Email service not initialized")
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Email service not initialized"})
// 	}

// 	if err := h.EmailService.SendConfirmationEmail(req.Email, confirmationURL); err != nil {
// 		log.Printf("Failed to send confirmation email: %v\n", err)
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to send confirmation email"})
// 	}

// 	return c.JSON(http.StatusOK, map[string]string{"message": "Password reset email sent.  Please check your inbox."})
// }

//Validasi Ke FetchData
// ResetPassword handles the reset password request
func (h *DashboardHandler) ResetPassword(c echo.Context) error {
	type ResetRequest struct {
		Email string `json:"email" validate:"required,email"`
	}

	req := new(ResetRequest)
	if err := c.Bind(req); err != nil {
		log.Printf("Binding error: %v", err)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	if err := c.Validate(req); err != nil {
		log.Printf("Validation error: %v", err)
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid email format"})
	}

	// Dapatkan Order ID dari environment
	orderID := GetOrderIDFromEnv()

	// Panggil FetchOrderData untuk mendapatkan data order
	orderData, err := h.FetchOrderData(orderID)
	if err != nil {
		log.Printf("Failed to fetch order data: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch order data"})
	}

	// Validasi email
	if orderData.Email != req.Email {
		log.Printf("Email mismatch: requested email %s, data email %s", req.Email, orderData.Email)
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Email tidak Sesuai!"})
	}

	// Generate a confirmation token (UUID)
	confirmationToken, err := generateConfirmationToken()
	if err != nil {
		log.Printf("Failed to generate confirmation token: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate confirmation token"})
	}

	// **Hapus token lama (jika ada) dan simpan token baru**
	err = h.replaceConfirmationToken(req.Email, confirmationToken)
	if err != nil {
		log.Printf("Failed to store confirmation token: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to store confirmation token"})
	}

	// Ambil HOSTNAME dari variabel lingkungan
	hostname := os.Getenv("HOSTNAME")
	if hostname == "" {
		log.Println("HOSTNAME environment variable not set")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "HOSTNAME environment variable not set"})
	}

	// Buat URL konfirmasi
	confirmationURL := fmt.Sprintf("https://live.upstream.id/%s/confirm-reset?token=%s", hostname, confirmationToken)
	log.Printf("Confirmation URL: %s", confirmationURL)

	// Send confirmation email
	if h.EmailService == nil {
		log.Println("Email service not initialized")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Email service not initialized"})
	}

	if err := h.EmailService.SendConfirmationEmail(req.Email, confirmationURL); err != nil {
		log.Printf("Failed to send confirmation email: %v\n", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to send confirmation email"})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Password reset email sent.  Please check your inbox."})
}

// //ConfirmReset handles the confirmation link click
// func (h *DashboardHandler) ConfirmReset(c echo.Context) error {
// 	token := c.QueryParam("token")
// 	log.Printf("ConfirmReset endpoint hit with token: %s", token)

// 	// Verify the token against the database
// 	email, err := h.verifyConfirmationToken(token, true) // true -> hapus token setelah verifikasi
// 	if err != nil {
// 		log.Printf("Invalid or expired confirmation token: %v", err)
// 		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid or expired confirmation token"})
// 	}
// 	log.Printf("Confirmation token verified for email: %s", email)

// 	// Token is valid, proceed with reset
// 	// On confirmation, delete the config.json file
// 	configPath := filepath.Join("config", "config.json")
// 	log.Printf("Attempting to delete config file: %s", configPath) // Tambahkan log

// 	err = os.Remove(configPath)
// 	if err != nil {
// 		if os.IsNotExist(err) {
// 			log.Printf("Config file not found: %s - This might be expected.", configPath)
// 			// Ganti dengan kode yang lebih baik
// 			// Jika berkas itu *opsional*, jangan kembalikan kesalahan.  Lanjutkan.
// 			// Jika berkas itu *wajib ada*, kembalikan kesalahan 404.
// 			// Contoh (opsional):
// 			log.Println("Continuing despite missing config file.")
// 		} else {
// 			log.Printf("Failed to delete config file: %v", err)
// 			return c.JSON(http.StatusInternalServerError, map[string]string{"error": fmt.Sprintf("Failed to delete config file: %v", err)})
// 		}
// 	} else {
// 		log.Printf("Config file deleted: %s", configPath)
// 	}

// 	// Restart the Docker container
// 	hostname := os.Getenv("HOSTNAME")
// 	if hostname == "" {
// 		log.Println("HOSTNAME environment variable not set")
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "HOSTNAME environment variable not set"})
// 	}

// 	// Construct the Docker restart command
// 	cmd := exec.Command("docker", "restart", hostname)
// 	log.Printf("Restarting Docker container with hostname: %s", hostname)

// 	// Run the command
// 	output, err := cmd.CombinedOutput()
// 	if err != nil {
// 		log.Printf("Failed to restart Docker container: %v, Output: %s", err, string(output))
// 		return c.JSON(http.StatusInternalServerError, map[string]string{"error": fmt.Sprintf("Failed to restart Docker container: %v", err)})
// 	}
// 	log.Printf("Docker container restarted successfully, Output: %s", string(output))

// 	// Redirect ke https://live.upstream.id/hostname/
// 	redirectURL := fmt.Sprintf("https://live.upstream.id/%s/ui/", hostname)
// 	log.Printf("Redirecting to: %s", redirectURL)
// 	return c.Redirect(http.StatusFound, redirectURL)  // StatusFound (302) adalah redirect sementara

// }

//ConfirmReset handles the confirmation link click
func (h *DashboardHandler) ConfirmReset(c echo.Context) error {
	token := c.QueryParam("token")
	log.Printf("ConfirmReset endpoint hit with token: %s", token)

	// Verifikasi token (ubah bagian ini untuk menggunakan verifyConfirmationToken)
	email, err := h.verifyConfirmationToken(token, true) // true -> hapus token setelah verifikasi
	if err != nil {
		log.Printf("Invalid or expired confirmation token: %v", err)
		// Tentukan nama file template yang akan digunakan
		templateName := "invalid_token.html"

		// Set template data (gunakan "" untuk RedirectURL karena tidak diperlukan)
		data := TemplateData{
			RedirectURL:  "",
			ErrorMessage: "Token tidak valid atau sudah kadaluarsa.",
		}

		// Load template
		tmpl, err := template.ParseFiles(templateName)
		if err != nil {
			log.Printf("Failed to parse template: %v", err)
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to parse template"})
		}

		// Set content type
		c.Response().Header().Set(echo.HeaderContentType, echo.MIMETextHTMLCharsetUTF8)

		// Execute template dan kirim sebagai respons
		err = tmpl.Execute(c.Response(), data)
		if err != nil {
			log.Printf("Failed to execute template: %v", err)
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to execute template"})
		}

		return nil
	}

	log.Printf("Confirmation token verified for email: %s", email)

	// Dapatkan hostname untuk URL pengalihan nanti
	hostname := os.Getenv("HOSTNAME")
	if hostname == "" {
		hostname = "localhost"
	}
	redirectURL := fmt.Sprintf("https://live.upstream.id/%s/ui/", hostname)

	// Load template
	tmpl, err := template.ParseFiles("waiting.html")
	if err != nil {
		log.Printf("Failed to parse template: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to parse template"})
	}

	// Create data untuk template
	data := TemplateData{
		RedirectURL: redirectURL,
		ErrorMessage: "", // Kosongkan pesan kesalahan jika token valid
	}

	// Set header dan jalankan template
	c.Response().Header().Set(echo.HeaderContentType, echo.MIMETextHTMLCharsetUTF8)
	err = tmpl.Execute(c.Response(), data)
	if err != nil {
		log.Printf("Failed to execute template: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to execute template"})
	}

	// ************************************************************************
	// Gunakan mekanisme ConfigReload yang sudah ada!
	// ************************************************************************

	// Remove config.json
	configPath := filepath.Join("config", "config.json")
	log.Printf("Attempting to delete config file: %s", configPath)

	err = os.Remove(configPath)
	if err != nil {
		if os.IsNotExist(err) {
			log.Printf("Config file not found: %s - This might be expected.", configPath)
			log.Println("Continuing despite missing config file.")
		} else {
			log.Printf("Failed to delete config file: %v", err)
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": fmt.Sprintf("Failed to delete config file: %v", err)})
		}
	} else {
		log.Printf("Config file deleted: %s", configPath)
	}

	// Trigger config reload
	log.Println("Meminta Config Reload dengan mengirimkan sinyal os.Interrupt")

	proc, err := os.FindProcess(os.Getpid())
	if err != nil {
		log.Printf("Failed to find process: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to signal config reload (process not found)"})
	}

	err = proc.Signal(os.Interrupt)
	if err != nil {
		log.Printf("Failed to signal process: %v", err)
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to signal config reload"})
	}

	log.Println("Config reload signaled successfully")

	// ************************************************************************
	// Akhir dari bagian kode ConfigReload.
	// ************************************************************************

	return nil
}

//Helper functions
func generateConfirmationToken() (string, error) {
	token, err := uuid.NewRandom()
	if err != nil {
		log.Printf("Failed to generate UUID: %v", err)
		return "", err
	}
	log.Printf("Generated confirmation token: %s", token.String())
	return token.String(), nil
}

// replaceConfirmationToken menghapus token lama (jika ada) dan menyimpan token baru.
func (h *DashboardHandler) replaceConfirmationToken(email, token string) error {
	// Use prepared statements to prevent SQL injection
	stmt, err := h.DB.Prepare("UPDATE client SET reset_token = ? WHERE email = ?")
	if err != nil {
		log.Printf("Failed to prepare statement: %v", err)
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(token, email)
	if err != nil {
		log.Printf("Failed to execute statement: %v", err)
		return err
	}
	log.Printf("Stored confirmation token for email: %s", email)
	return nil
}

// verifyConfirmationToken memverifikasi token dan secara opsional menghapusnya.
func (h *DashboardHandler) verifyConfirmationToken(token string, deleteToken bool) (string, error) {
	// Use prepared statements to prevent SQL injection
	stmt, err := h.DB.Prepare("SELECT email FROM client WHERE reset_token = ?")
	if err != nil {
		log.Printf("Failed to prepare statement: %v", err)
		return "", err
	}
	defer stmt.Close()

	var email string
	err = stmt.QueryRow(token).Scan(&email)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Println("Confirmation token not found")
		} else {
			log.Printf("QueryRow scan error: %v", err)
		}
		return "", err
	}
	log.Printf("Confirmation token verified for email: %s", email)

	if deleteToken {
		err = h.clearConfirmationToken(email)
		if err != nil {
			log.Printf("Failed to clear confirmation token: %v", err)
			// Log error, tapi jangan gagalkan permintaan.  Token sudah divalidasi.
		} else {
			log.Println("Confirmation token cleared after verification.")
		}
	}

	return email, nil
}


func (h *DashboardHandler) clearConfirmationToken(email string) error {
	// Use prepared statements to prevent SQL injection
	stmt, err := h.DB.Prepare("UPDATE client SET reset_token = NULL WHERE email = ?")
	if err != nil {
		log.Printf("Failed to prepare statement: %v", err)
		return err
	}
	defer stmt.Close()

	_, err = stmt.Exec(email)
	if err != nil {
		log.Printf("Failed to execute statement: %v", err)
		return err
	}
	log.Printf("Confirmation token cleared for email: %s", email)
	return nil
}