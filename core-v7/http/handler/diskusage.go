package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/datarhei/core/v16/log"
	"github.com/labstack/echo/v4"

	"github.com/shirou/gopsutil/disk" // Import gopsutil
)

// DiskUsageHandler adalah struct untuk handler disk usage.
type DiskUsageHandler struct {
	Logger log.Logger
}

// DiskUsageCache menyimpan data penggunaan disk yang di-cache.
type DiskUsageCache struct {
	Usage     DiskUsage
	CacheTime int64
}

// DiskUsage menyimpan data penggunaan disk.
type DiskUsage struct {
	UsedDisk  string `json:"usedDisk"`
	TotalDisk string `json:"totalDisk"`
	FreeDisk  string `json:"freeDisk"`
}

// NewDiskUsageHandler membuat instance baru dari DiskUsageHandler.
func NewDiskUsageHandler(logger log.Logger) *DiskUsageHandler {
	return &DiskUsageHandler{
		Logger: logger,
	}
}

// formatSize memformat ukuran byte ke dalam format yang mudah dibaca (GB atau MB).
func formatSize(bytes uint64) string {
	const (
		KB = 1024
		MB = KB * 1024
		GB = MB * 1024
	)
	if bytes >= GB {
		return fmt.Sprintf("%.2f GB", float64(bytes)/float64(GB))
	}
	return fmt.Sprintf("%.2f MB", float64(bytes)/float64(MB))
}

// getDiskUsage mendapatkan informasi penggunaan disk.
func getDiskUsage(path string) (*DiskUsage, error) {
	usage, err := disk.Usage(path)
	if err != nil {
		return nil, err
	}

	diskUsage := &DiskUsage{
		UsedDisk:  formatSize(usage.Used),
		TotalDisk: formatSize(usage.Total),
		FreeDisk:  formatSize(usage.Free),
	}

	return diskUsage, nil
}

// respondWithError mengirimkan respons kesalahan JSON.
func respondWithError(c echo.Context, code int, message string) error {
	return respondWithJSON(c, code, map[string]string{"error": message})
}

// respondWithJSON mengirimkan respons JSON.
func respondWithJSON(c echo.Context, code int, payload interface{}) error {
	response, _ := json.Marshal(payload)

	// Enable CORS - Adjust as needed for production
	c.Response().Header().Set("Access-Control-Allow-Origin", "*")
	c.Response().Header().Set("Content-Type", "application/json")
	c.Response().WriteHeader(code)
	_, err := c.Response().Write(response)

	return err
}

// HandleDiskUsage menangani permintaan untuk penggunaan disk.
func (h *DiskUsageHandler) HandleDiskUsage(c echo.Context) error {
	const (
		cacheDuration int64 = 100000 // 1 menit dalam milidetik
	)

	var (
		diskPath        string        = "/" // Sesuaikan dengan partisi yang benar
		cachedDiskUsage DiskUsageCache
	)
	now := time.Now().UnixMilli()

	// Periksa cache
	if cachedDiskUsage.CacheTime > 0 && (now-cachedDiskUsage.CacheTime < cacheDuration) {
		fmt.Println("Using cached disk usage")
		return respondWithJSON(c, http.StatusOK, cachedDiskUsage.Usage)
	}

	// Dapatkan penggunaan disk
	usage, err := getDiskUsage(diskPath)
	if err != nil {
		fmt.Printf("Gagal mendapatkan disk usage: %v\n", err)
		return respondWithError(c, http.StatusInternalServerError, "Gagal mendapatkan disk usage")
	}

	// Update cache
	cachedDiskUsage = DiskUsageCache{
		Usage:     *usage,
		CacheTime: now,
	}

	return respondWithJSON(c, http.StatusOK, *usage)
}