package handler

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"
	"time"
)

// Struktur data yang lebih sederhana, hanya perlu field yang digunakan
type LiveEntry struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Order     string `json:"order"`
}

// Struktur untuk menyimpan hasil perhitungan live details
type LiveDetails struct {
	TotalLive     int `json:"total_live"`
	LiveAktif     int `json:"live_aktif"`
	LiveTerjadwal int `json:"live_terjadwal"`
	LiveSelesai   int `json:"live_selesai"`
	LiveSet       int `json:"live_set"`
}

// Function untuk membaca data dari data.json
func readDataFromFile(filename string) (map[string]interface{}, error) {
	jsonFile, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("gagal membuka file %s: %w", filename, err)
	}
	defer jsonFile.Close()

	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("gagal membaca file %s: %w", filename, err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(byteValue, &result)
	if err != nil {
		return nil, fmt.Errorf("gagal unmarshal JSON dari file %s: %w", filename, err)
	}

	return result, nil
}

// Function untuk menghitung live details
func CalculateLiveDetails(filename string) (LiveDetails, error) {
	data, err := readDataFromFile(filename)
	if err != nil {
		return LiveDetails{}, err
	}

	totalLive := 0
	liveAktif := 0
	liveTerjadwal := 0
	liveSelesai := 0
	liveSet := 0

	// Load lokasi waktu WIB
	wibLoc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return LiveDetails{}, fmt.Errorf("gagal memuat lokasi waktu WIB: %w", err)
	}

	now := time.Now().In(wibLoc)
	log.Println("Waktu sekarang (WIB):", now.Format(time.RFC3339)) // Tambahkan log waktu sekarang

	// Akses ke bagian "process" dari data JSON
	processData, ok := data["process"].(map[string]interface{})
	if !ok {
		return LiveDetails{}, fmt.Errorf("bagian 'process' tidak ditemukan atau bukan map[string]interface{}")
	}

	for key, entry := range processData {
		if entry == nil {
			log.Printf("Skipping entri dengan key '%s' karena nil", key)
			continue // Skip jika entri adalah null
		}

		//totalLive
		if strings.Contains(key, "upstream-id:egress:") {
			totalLive++
			log.Printf("Menemukan totalLive dengan key '%s', totalLive sekarang: %d", key, totalLive)
		}

		//liveset - Hanya hitung yang tanpa _snapshot
		if strings.Contains(key, "upstream-id:ingest:") && !strings.Contains(key, "_snapshot") {
			liveSet++
			log.Printf("Menemukan liveSet dengan key '%s', liveSet sekarang: %d", key, liveSet)
		}

		// Konversi entry ke map[string]interface{} untuk mengakses properti
		entryMap, ok := entry.(map[string]interface{})
		if !ok {
			log.Printf("Entri dengan key '%s' bukan map[string]interface{}, skipping", key)
			continue
		}

		//Liveaktif
		order, ok := entryMap["order"].(string)
		if ok && strings.ToLower(order) == "start" && strings.Contains(key, "upstream-id:egress:") {
			liveAktif++
			log.Printf("Menemukan liveAktif dengan key '%s', liveAktif sekarang: %d", key, liveAktif)
		}

		// Ambil nilai "start_time" dan "end_time"
		startTimeStr, ok := entryMap["start_time"].(string)
		endTimeStr, ok := entryMap["end_time"].(string)

		startTime, err := parseTimeWIB(startTimeStr, wibLoc)
		if err != nil && startTimeStr != "" {
			log.Printf("Gagal memparse start_time untuk %s: %v", key, err)
			continue
		}

		endTime, err := parseTimeWIB(endTimeStr, wibLoc)
		if err != nil && endTimeStr != "" {
			log.Printf("Gagal memparse end_time untuk %s: %v", key, err)
			continue
		}

		if strings.Contains(key, "upstream-id:egress:") {

			//liveTerjadwal
			if startTimeStr != "" && startTime.After(now) {
				liveTerjadwal++
				log.Printf("Menemukan liveTerjadwal dengan key '%s', startTime: %s, liveTerjadwal sekarang: %d", key, startTime.Format(time.RFC3339), liveTerjadwal)
			}
			//LiveSelesai
			if endTimeStr != "" && startTimeStr != "" {
				if endTime.Before(now) && startTime.Before(now) {
					liveSelesai++
					log.Printf("Menemukan liveSelesai dengan key '%s', endTime: %s, startTime: %s, liveSelesai sekarang: %d", key, endTime.Format(time.RFC3339), startTime.Format(time.RFC3339), liveSelesai)
				}
			}

		}
	}

	log.Println("Hasil perhitungan:")
	log.Println("TotalLive:", totalLive)
	log.Println("LiveAktif:", liveAktif)
	log.Println("LiveTerjadwal:", liveTerjadwal)
	log.Println("LiveSelesai:", liveSelesai)
	log.Println("LiveSet:", liveSet)

	return LiveDetails{
		TotalLive:     totalLive,
		LiveAktif:     liveAktif,
		LiveTerjadwal: liveTerjadwal,
		LiveSelesai:   liveSelesai,
		LiveSet:       liveSet,
	}, nil
}

// Function untuk memparse waktu dengan lokasi waktu WIB
func parseTimeWIB(timeStr string, loc *time.Location) (time.Time, error) {
	// Tambahkan tanggal hari ini jika hanya waktu yang diberikan
	if !strings.Contains(timeStr, "-") {
		now := time.Now().In(loc)
		timeStr = now.Format("2006-01-02T") + timeStr
	}

	// Menggunakan layout yang lebih umum
	layouts := []string{
		"2006-01-02T15:04:05Z07:00", // Layout dengan timezone
		"2006-01-02T15:04",        // Layout tanpa detik
		"2006-01-02T15:04:05", // Layout tanpa timezone offset
		"15:04",                    // Layout hanya jam dan menit
		"2006-01-02 15:04:05Z07:00", // Layout dengan spasi
		"2006-01-02 15:04", //Layout dengan spasi tanpa detik
		"2006-01-02 15:04:05", //Layout dengan spasi tanpa offset
	}

	var t time.Time
	var err error

	for _, layout := range layouts {
		t, err = time.ParseInLocation(layout, timeStr, loc)
		if err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("gagal memparse waktu %s: %w", timeStr, err)

}

func GetLiveDetails(filename string) (LiveDetails, error) {
	return CalculateLiveDetails(filename)
}