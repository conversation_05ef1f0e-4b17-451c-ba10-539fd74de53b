package handler

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"

	"github.com/labstack/echo/v4"
	"github.com/datarhei/core/v16/http/models" // Ganti dengan path yang benar
)

// GetUnassignedMetadata mengembalikan daftar metadata file yang belum memiliki ChannelID.
func (h *UploadHandler) GetUnassignedMetadata(c echo.Context) error {
	metadataFile := filepath.Join(h.uploadDir, "metadata.json")
	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to load metadata: " + err.Error()})
	}

	// Filter metadata untuk hanya menyertakan file tanpa ChannelID
	var files []map[string]interface{}
	for _, metadata := range metadataList {
		if metadata.ChannelID == "" {
			files = append(files, map[string]interface{}{
				"Id":            metadata.UploadID,
				"original_name": metadata.FileName,
				"size":          metadata.FileSize,
				"duration":      metadata.Duration, //tambahan
			})
		}
	}

	return c.J<PERSON>(http.StatusOK, files)
}

// SelectFile menangani permintaan untuk memilih file yang sudah ada dan menetapkannya ke channel.
func (h *UploadHandler) SelectFile(c echo.Context) error {
	var req struct {
		UploadID  string `json:"uploadId"` // Menggunakan UploadID sebagai identifier
		ChannelID string `json:"channelId"`
	}

	if err := c.Bind(&req); err != nil {
		fmt.Printf("Error binding request: %s\n", err.Error())
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request: " + err.Error()})
	}

	if req.UploadID == "" || req.ChannelID == "" {
		fmt.Println("UploadID and ChannelID are required")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "UploadID and ChannelID are required"})
	}

	metadataFile := filepath.Join(h.uploadDir, "metadata.json")
	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		fmt.Printf("Error loading metadata: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to load metadata: " + err.Error()})
	}

	// Cari metadata yang sesuai dengan UploadID
	var selectedMetadata *models.FileMetadata
	var selectedIndex = -1
	for i, metadata := range metadataList {
		if metadata.UploadID == req.UploadID {
			selectedMetadata = &metadata
			selectedIndex = i
			break
		}
	}

	if selectedMetadata == nil {
		fmt.Printf("Metadata not found for UploadID: %s\n", req.UploadID)
		return c.JSON(http.StatusNotFound, map[string]string{"error": "Metadata not found for UploadID: " + req.UploadID})
	}

	sourcePath := selectedMetadata.FilePath
	destDir := filepath.Join("data/channels", req.ChannelID)
	destPath := filepath.Join(destDir, "videoloop.source")

	fmt.Printf("Source path: %s\n", sourcePath)
	fmt.Printf("Destination path: %s\n", destPath)

	// Pastikan file sumber ada
	if _, err := os.Stat(sourcePath); os.IsNotExist(err) {
		fmt.Printf("File not found: %s\n", sourcePath)
		return c.JSON(http.StatusNotFound, map[string]string{"error": "File not found: " + selectedMetadata.FileName})
	}

	// Load metadata
	// metadataList, err := h.loadMetadataFromFile(metadataFile) // Sudah di load di awal function
	// if err != nil {
	// 	fmt.Printf("Error loading metadata: %s\n", err.Error())
	// 	return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to load metadata: " + err.Error()})
	// }

	// Periksa apakah channel sudah memiliki file yang terkait
	var oldUploadID string
	for i, metadata := range metadataList {
		if metadata.ChannelID == req.ChannelID {
			oldUploadID = metadata.UploadID
			metadataList[i].ChannelID = "" // Hapus channel ID dari metadata yang lama
			break
		}
	}

	// Jika ada file lama, pindahkan kembali ke data/uploads
	if oldUploadID != "" {
		var oldMetadata *models.FileMetadata
		for i, metadata := range metadataList{
			if metadata.UploadID == oldUploadID{
				oldMetadata = &metadataList[i]
				break
			}
		}
		oldFilePath := filepath.Join(destDir, "videoloop.source")
		originalFilePath := oldMetadata.FilePath //filepath.Join(h.uploadDir, oldMetadata.FileName) // gunakan file path dari metadata
		

		err = os.Rename(oldFilePath, originalFilePath)
		if err != nil && !os.IsNotExist(err) {
			fmt.Printf("Failed to move old file back to uploads: %s\n", err.Error())
			// Log error ini tetapi jangan hentikan proses
		} else {
			fmt.Printf("Moved old file back to uploads: %s\n", originalFilePath)
		}
	}

	// Buat direktori tujuan jika belum ada
	if err := os.MkdirAll(destDir, 0755); err != nil {
		fmt.Printf("Failed to create channel directory: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create channel directory: " + err.Error()})
	}

	// Pindahkan file yang baru
	err = os.Rename(sourcePath, destPath)
	if err != nil {
		fmt.Printf("Failed to move file: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to move file: " + err.Error()})
	}

	// Perbarui metadata untuk channel ID yang baru
	// for i, metadata := range metadataList { //Gunakan index yang sudah di dapat dari looping awal
	// 	if metadata.UploadID == req.UploadID {
	// 		metadataList[i].ChannelID = req.ChannelID
	// 		break
	// 	}
	// }
	metadataList[selectedIndex].ChannelID = req.ChannelID

	// Simpan metadata
	err = h.saveAllMetadataToFile(metadataList, metadataFile)
	if err != nil {
		fmt.Printf("Error saving metadata: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to save metadata: " + err.Error()})
	}

	// Berikan respons sukses
	return c.JSON(http.StatusOK, map[string]string{
		"status": "ok",
		"path":   "data/channels/" + req.ChannelID + "/videoloop.source",
	})
}

// DeleteSelectedFile digunakan jika file yang akan dihapus adalah file videoloop.source yang sedang digunakan oleh channel
func (h *UploadHandler) DeleteSelectedFile(c echo.Context) error {
	var req struct {
		UploadID  string `json:"uploadId"`
		ChannelID string `json:"channelId"`
	}

	if err := c.Bind(&req); err != nil {
		fmt.Printf("Error binding request: %s\n", err.Error())
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request"})
	}

	channelFilePath := filepath.Join("data/channels", req.ChannelID, "videoloop.source")

	// Cek apakah file videoloop.source ada
	_, err := os.Stat(channelFilePath)
	if os.IsNotExist(err) {
		fmt.Printf("Channel file not found: %s\n", channelFilePath)
		return c.JSON(http.StatusNotFound, map[string]string{"error": "File tidak ditemukan di direktori channel"})
	}

	// Hapus file videoloop.source
	err = os.Remove(channelFilePath)
	if err != nil {
		fmt.Printf("Error deleting channel file: %s, error: %s\n", channelFilePath, err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Gagal menghapus file dari direktori channel: " + err.Error()})
	}

	fmt.Printf("Channel file deleted successfully: %s\n", channelFilePath)

	metadataFile := filepath.Join(h.uploadDir, "metadata.json")
	// Load metadata
	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		fmt.Printf("Error loading metadata: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to load metadata: " + err.Error()})
	}

	// Cari index metadata yang sesuai
	metadataIndex := -1
	for i, metadata := range metadataList {
		if metadata.UploadID == req.UploadID && metadata.ChannelID == req.ChannelID {
			metadataIndex = i
			break
		}
	}

	// Jika metadata tidak ditemukan, kembalikan error
	if metadataIndex == -1 {
		fmt.Printf("Metadata tidak ditemukan untuk file: %s dan channel: %s\n", req.UploadID, req.ChannelID)
		return c.JSON(http.StatusNotFound, map[string]string{"error": "Metadata tidak ditemukan"})
	}

	//Hapus Channel ID
	metadataList[metadataIndex].ChannelID = ""

	// Simpan metadata
	err = h.saveAllMetadataToFile(metadataList, metadataFile)
	if err != nil {
		fmt.Printf("Error saving metadata: %s\n", err.Error())
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to save metadata: " + err.Error()})
	}

	fmt.Printf("Metadata deleted successfully for file: %s, index: %d\n", req.UploadID, metadataIndex)

	return c.JSON(http.StatusOK, map[string]string{"message": "File berhasil dihapus dari channel"})
}

