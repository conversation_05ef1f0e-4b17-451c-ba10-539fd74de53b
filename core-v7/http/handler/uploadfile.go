package handler

import (
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/datarhei/core/v16/http/models"
	"github.com/datarhei/core/v16/http/handler/util"
)

type UploadHandler struct {
	uploadDir          string
	uploading          map[string]bool
	uploadingLock      sync.Mutex
	progress           map[string]float64
	progressLock       sync.RWMutex // Gunakan RWMutex untuk concurrent access
	fileMetadata       map[string]models.FileMetadata
	metadataLock       sync.Mutex
	configDir          string
	redirectURI        string
	maxFileSize        int64
	gdriveClientID     string
	gdriveClientSecret string
	uiBaseURL          string
	hostname           string
}

// Asumsikan ErrorResponse didefinisikan di sini
type ErrorResponse struct {
	Message string `json:"message"`
	Error   string `json:"error"`
}

// Tambahkan errorResponse sebagai metode dari UploadHandler
func (h *UploadHandler) errorResponse(c echo.Context, code int, message string, details error) error {
	var errStr string
	if details != nil {
		errStr = details.Error()
	}
	response := ErrorResponse{
		Message: message,
		Error:   errStr,
	}
	return c.JSON(code, response)
}

func NewUploadHandler(uploadDir string, configDir string, maxFileSize int64, gdriveClientID string, gdriveClientSecret string, uiBaseURL string) *UploadHandler {
	hostname := os.Getenv("HOSTNAME")
	if hostname == "" {
		hostname = "localhost"
	}

	return &UploadHandler{
		uploadDir:          uploadDir,
		uploading:          make(map[string]bool),
		progress:           make(map[string]float64),
		fileMetadata:       make(map[string]models.FileMetadata),
		configDir:          configDir,
		redirectURI:        os.Getenv("GDRIVE_REDIRECT_URI"),
		maxFileSize:        maxFileSize,
		gdriveClientID:     gdriveClientID,
		gdriveClientSecret: gdriveClientSecret,
		uiBaseURL:          uiBaseURL,
		hostname:           hostname,
	}
}

func (h *UploadHandler) UploadFile(c echo.Context) error {
	fileHeader, err := c.FormFile("file")
	if err != nil {
		fmt.Printf("UploadFile: Error getting file from form: %s\n", err)
		return h.errorResponse(c, http.StatusBadRequest, "Failed to get file from form", err)
	}

	chunkIndexStr := c.FormValue("chunk")
	totalChunksStr := c.FormValue("totalChunks")
	fileName := c.FormValue("filename")
	channelID := c.FormValue("channelId")
	uploadID := c.FormValue("uploadId")

	if fileName == "" {
		fmt.Println("UploadFile: Filename is required")
		return h.errorResponse(c, http.StatusBadRequest, "Filename is required", nil)
	}

	chunkIndex, err := strconv.Atoi(chunkIndexStr)
	if err != nil {
		fmt.Printf("UploadFile: Invalid chunk index: %s\n", err)
		return h.errorResponse(c, http.StatusBadRequest, "Invalid chunk index", err)
	}

	totalChunks, err := strconv.Atoi(totalChunksStr)
	if err != nil {
		fmt.Printf("UploadFile: Invalid total chunks: %s\n", err)
		return h.errorResponse(c, http.StatusBadRequest, "Invalid total chunks", err)
	}

	if uploadID == "" {
		uploadID = uuid.New().String()
		fmt.Printf("UploadFile: Generated new upload ID: %s\n", uploadID)
	}

	if uploadID == "" {
		fmt.Println("UploadFile: Upload ID is missing")
		return h.errorResponse(c, http.StatusBadRequest, "Upload ID is missing", nil)
	}

	h.uploadingLock.Lock()
	_, uploading := h.uploading[uploadID]
	if !uploading && chunkIndex > 0 {
		h.uploadingLock.Unlock()
		fmt.Printf("UploadFile: Invalid chunk sequence, upload not started. uploadID: %s, chunkIndex: %d\n", uploadID, chunkIndex)
		return h.errorResponse(c, http.StatusBadRequest, "Invalid chunk sequence, upload not started", fmt.Errorf("uploadID: %s, chunkIndex: %d", uploadID, chunkIndex))
	}

	if chunkIndex == 0 {
		h.uploading[uploadID] = true
		fmt.Printf("UploadFile: Starting upload with uploadID: %s, filename: %s\n", uploadID, fileName)
	}
	h.uploadingLock.Unlock()

	// Channel untuk mengumpulkan error dari goroutine
	errChan := make(chan error, 1)
	// WaitGroup untuk menunggu semua goroutine selesai
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		file, err := fileHeader.Open()
		if err != nil {
			fmt.Printf("Goroutine: Failed to open file: %s\n", err)
			errChan <- fmt.Errorf("failed to open file: %w", err)
			return
		}
		defer file.Close()

		// Proses chunk dalam goroutine
		fmt.Printf("Goroutine: Processing chunk %d for uploadID: %s\n", chunkIndex, uploadID)
		if err := h.processChunk(file, fileName, chunkIndex, totalChunks, uploadID); err != nil {
			fmt.Printf("Goroutine: Error processing chunk %d for uploadID %s: %s\n", chunkIndex, uploadID, err)
			errChan <- err
			return
		}
		fmt.Printf("Goroutine: Successfully processed chunk %d for uploadID: %s\n", chunkIndex, uploadID)
		errChan <- nil
	}()

	// Non-blocking read from the error channel (with timeout)
	select {
	case err := <-errChan:
		if err != nil {
			// Hapus chunk dan folder jika ada error
			h.cleanupChunks(uploadID, fileName, totalChunks)
			h.uploadingLock.Lock()
			delete(h.uploading, uploadID)
			h.uploadingLock.Unlock()

			fmt.Printf("UploadFile: Error from goroutine: %s\n", err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to process chunk", err)
		}
	case <-time.After(5 * time.Second): // Timeout jika tidak ada error dalam 5 detik
		// Jika tidak ada error dalam 5 detik, lanjutkan
	}

	// Hitung dan simpan progress
	progress := float64(chunkIndex+1) / float64(totalChunks) * 100
	h.setProgress(uploadID, progress)
	fmt.Printf("UploadFile: Progress for uploadID %s: %.2f%%\n", uploadID, progress)

	// Jika ini adalah chunk terakhir, gabungkan file dan simpan metadata
	if chunkIndex == totalChunks-1 {
		fmt.Printf("UploadFile: Last chunk received for uploadID: %s, waiting for goroutines to complete...\n", uploadID)
		wg.Wait() // Wait for all chunk processing goroutines to complete
		fmt.Printf("UploadFile: All goroutines completed for uploadID: %s\n", uploadID)

		// Merge chunks
		filePath, err := h.mergeChunks(fileName, totalChunks, uploadID)
		if err != nil {
			// Hapus chunk dan folder jika merge gagal
			h.cleanupChunks(uploadID, fileName, totalChunks)

			h.progressLock.Lock()
			delete(h.progress, uploadID)
			h.progressLock.Unlock()

			fmt.Printf("UploadFile: Failed to merge chunks for uploadID %s: %s\n", uploadID, err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to merge chunks", err)
		}
		fmt.Printf("UploadFile: Successfully merged chunks for uploadID: %s, file path: %s\n", uploadID, filePath)

		// Open the merged file
		mergedFile, err := os.Open(filePath)
		if err != nil {
			// Hapus chunk dan folder jika open merged file gagal
			h.cleanupChunks(uploadID, fileName, totalChunks)
			fmt.Printf("UploadFile: Failed to open merged file for uploadID %s: %s\n", uploadID, err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to open merged file", err)
		}
		defer mergedFile.Close()

		// Get the file size
		fileInfo, err := mergedFile.Stat()
		if err != nil {
			// Hapus chunk dan folder jika get file size gagal
			h.cleanupChunks(uploadID, fileName, totalChunks)
			fmt.Printf("UploadFile: Failed to get merged file size for uploadID %s: %s\n", uploadID, err)
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to get merged file size", err)
		}
		fileSize := fileInfo.Size()
		fmt.Printf("UploadFile: Merged file size for uploadID %s: %d\n", uploadID, fileSize)

		// Get video duration
		duration, err := util.GetVideoDuration(mergedFile, fileSize)
		if err != nil {
			fmt.Printf("UploadFile: Error getting video duration for uploadID %s: %s\n", uploadID, err)
			duration = "00:00:00" // Set default if failed to get duration
		}
		fmt.Printf("UploadFile: Video duration for uploadID %s: %s\n", uploadID, duration)

		// Set ChannelID only if channelID is not empty
		var setChannelID string
		if strings.TrimSpace(channelID) != "" {
			setChannelID = channelID
		}
		metadata := models.FileMetadata{
			ChannelID: setChannelID,
			FileName:  fileName,
			FileSize:  fileSize,
			Duration:  duration,
			UploadID:  uploadID,
			FilePath:  filePath,
		}

		h.setFileMetadata(uploadID, metadata)
		fmt.Printf("UploadFile: Metadata set for uploadID %s: %+v\n", uploadID, metadata)

		// Save metadata to file
		metadataFile := filepath.Join(h.uploadDir, "metadata.json")
		if err := h.saveMetadataToFile(metadata, metadataFile); err != nil {
			fmt.Printf("UploadFile: Error saving metadata to file for uploadID %s: %s\n", uploadID, err)
		}
		fmt.Printf("UploadFile: Metadata saved to file for uploadID %s\n", uploadID)

		h.progressLock.Lock()
		delete(h.progress, uploadID)
		h.progressLock.Unlock()

		h.uploadingLock.Lock()
		delete(h.uploading, uploadID)
		h.uploadingLock.Unlock()

		fmt.Printf("UploadFile: Upload completed successfully for uploadID: %s\n", uploadID)
		return c.JSON(http.StatusOK, map[string]interface{}{
			"message":   "File uploaded successfully",
			"upload_id": uploadID,
			"metadata":  metadata,
			"progress":  100,
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{"message": "Chunk uploaded", "progress": progress, "upload_id": uploadID})
}

// cleanupChunks menghapus semua chunk dan direktori chunk jika upload gagal
func (h *UploadHandler) cleanupChunks(uploadID string, fileName string, totalChunks int) {
	chunkDir := filepath.Join(h.uploadDir, uploadID)

	for i := 0; i < totalChunks; i++ {
		chunkPath := filepath.Join(chunkDir, fmt.Sprintf("%s.part%03d", fileName, i))
		if err := os.Remove(chunkPath); err != nil && !os.IsNotExist(err) {
			fmt.Printf("CleanupChunks: Failed to delete chunk file %s: %s\n", chunkPath, err)
		}
	}

	// Remove the chunk directory
	if err := os.RemoveAll(chunkDir); err != nil && !os.IsNotExist(err) {
		fmt.Printf("CleanupChunks: Failed to delete chunk directory %s: %s\n", chunkDir, err)
	}

	fmt.Printf("CleanupChunks: Cleaned up chunks for uploadID: %s\n", uploadID)
}

func (h *UploadHandler) processChunk(file multipart.File, fileName string, chunkIndex int, totalChunks int, uploadID string) error {
	// Simpan chunk
	if err := h.saveChunk(file, fileName, chunkIndex, totalChunks, uploadID); err != nil {
		fmt.Printf("ProcessChunk: Failed to save chunk %d for uploadID %s: %s\n", chunkIndex, uploadID, err)
		return fmt.Errorf("failed to save chunk: %w", err)
	}
	return nil
}

// GetProgress mengembalikan progress upload.
func (h *UploadHandler) GetProgress(c echo.Context) error {
	uploadID := c.QueryParam("uploadId")
	if uploadID == "" {
		return h.errorResponse(c, http.StatusBadRequest, "Upload ID is required", nil)
	}

	h.progressLock.Lock()
	progress, ok := h.progress[uploadID]
	h.progressLock.Unlock()

	if !ok {
		return h.errorResponse(c, http.StatusNotFound, "Progress not found for upload ID", nil)
	}

	return c.JSON(http.StatusOK, map[string]float64{"progress": progress})
}

// saveChunk menyimpan setiap chunk file ke disk.
func (h *UploadHandler) saveChunk(file multipart.File, fileName string, chunkIndex int, totalChunks int, uploadID string) error {
	chunkDir := filepath.Join(h.uploadDir, uploadID)
	if _, err := os.Stat(chunkDir); os.IsNotExist(err) {
		if err := os.MkdirAll(chunkDir, 0755); err != nil {
			return fmt.Errorf("failed to create chunk directory: %w", err)
		}
	}

	chunkPath := filepath.Join(chunkDir, fmt.Sprintf("%s.part%03d", fileName, chunkIndex)) // Tambahkan padding nol

	outFile, err := os.Create(chunkPath)
	if err != nil {
		return fmt.Errorf("failed to create chunk file: %w", err)
	}
	defer outFile.Close()

	_, err = io.Copy(outFile, file)
	if err != nil {
		return fmt.Errorf("failed to copy chunk data: %w", err)
	}
	return nil
}

// mergeChunks menggabungkan semua chunk menjadi satu file.
func (h *UploadHandler) mergeChunks(fileName string, totalChunks int, uploadID string) (string, error) {
	chunkDir := filepath.Join(h.uploadDir, uploadID)
	mergedFilePath := filepath.Join(h.uploadDir, fileName)

	mergedFile, err := os.Create(mergedFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to create merged file: %w", err)
	}
	defer mergedFile.Close()

	for i := 0; i < totalChunks; i++ {
		chunkPath := filepath.Join(chunkDir, fmt.Sprintf("%s.part%03d", fileName, i)) // Sesuaikan dengan format penamaan chunk
		chunkFile, err := os.Open(chunkPath)
		if err != nil {
			return "", fmt.Errorf("failed to open chunk file %d: %w", i, err)
		}
		defer chunkFile.Close()

		_, err = io.Copy(mergedFile, chunkFile)
		if err != nil {
			return "", fmt.Errorf("failed to copy chunk %d to merged file: %w", i, err)
		}

		// Hapus chunk setelah digabungkan
		os.Remove(chunkPath)
	}

	// Hapus direktori chunk
	os.RemoveAll(chunkDir)

	return mergedFilePath, nil
}

func (h *UploadHandler) setProgress(uploadID string, progress float64) {
	h.progressLock.Lock()
	defer h.progressLock.Unlock()
	h.progress[uploadID] = progress
}

func (h *UploadHandler) setFileMetadata(uploadID string, metadata models.FileMetadata) {
	h.metadataLock.Lock()
	defer h.metadataLock.Unlock()
	h.fileMetadata[uploadID] = metadata
}

func (h *UploadHandler) GetFileMetadata(uploadID string) (models.FileMetadata, bool) {
	h.metadataLock.Lock()
	defer h.metadataLock.Unlock()
	metadata, ok := h.fileMetadata[uploadID]
	return metadata, ok
}

func (h *UploadHandler) saveMetadataToFile(metadata models.FileMetadata, metadataFile string) error {
	// Baca metadata yang sudah ada dari file
	var existingMetadata []models.FileMetadata
	file, err := os.OpenFile(metadataFile, os.O_RDWR|os.O_CREATE, 0644)
	if err != nil {
		return fmt.Errorf("failed to open metadata file: %w", err)
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	if fileInfo.Size() > 0 {
		decoder := json.NewDecoder(file)
		err = decoder.Decode(&existingMetadata)
		if err != nil {
			return fmt.Errorf("failed to decode existing metadata: %w", err)
		}
	}

	// Tambahkan metadata baru ke daftar yang sudah ada
	existingMetadata = append(existingMetadata, metadata)

	// Encode semua metadata (yang lama dan yang baru) ke JSON
	data, err := json.MarshalIndent(existingMetadata, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal metadata to JSON: %w", err)
	}

	// Truncate file
	err = file.Truncate(0)
	if err != nil {
		return fmt.Errorf("failed to truncate file: %w", err)
	}

	// Set offset ke awal file
	_, err = file.Seek(0, 0)
	if err != nil {
		return fmt.Errorf("failed to seek to beginning of file: %w", err)
	}

	// Tulis data JSON ke file
	_, err = file.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write metadata to file: %w", err)
	}

	return nil
}

// DeleteFile menghapus file dan metadatanya
func (h *UploadHandler) DeleteFile(c echo.Context) error {
	fileId := c.Param("fileId")

	if fileId == "" {
		return h.errorResponse(c, http.StatusBadRequest, "File ID is required", nil)
	}

	metadataFile := filepath.Join(h.uploadDir, "metadata.json")

	// Load metadata from file
	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to load metadata", err)
	}

	// Find the metadata entry with the matching UploadID
	var foundIndex = -1
	var metadataToDelete models.FileMetadata
	for i, metadata := range metadataList {
		if metadata.UploadID == fileId {
			foundIndex = i
			metadataToDelete = metadata
			break
		}
	}

	if foundIndex == -1 {
		// Metadata tidak ditemukan, hapus saja
		fmt.Println("Metadata not found, removing metadata entry.")
		if err := h.deleteMetadataEntry(metadataList, metadataFile, fileId); err != nil {
			return h.errorResponse(c, http.StatusInternalServerError, "Failed to delete metadata entry", err)
		}
		return h.errorResponse(c, http.StatusNotFound, "File metadata not found", nil)
	}

	// Determine the channel ID and original file name
	channelID := metadataToDelete.ChannelID
	originalFileName := metadataToDelete.FileName

	// Determine the possible file paths
	var possibleFilePaths []string

	// Check if the file is in a channel directory (with the renamed file name)
	if channelID != "" {
		channelDir := filepath.Join("data", "channels", channelID)
		possibleFilePaths = append(possibleFilePaths, filepath.Join(channelDir, "videoloop.source"))
	}

	// Check if the file is in the upload directory (with the original file name)
	possibleFilePaths = append(possibleFilePaths, filepath.Join(h.uploadDir, originalFileName))

	// Try to delete the file from the possible locations
	var fileDeleted bool
	for _, path := range possibleFilePaths {
		if _, err := os.Stat(path); !os.IsNotExist(err) {
			// File exists, try to delete it
			if err := os.Remove(path); err == nil {
				fmt.Printf("File deleted successfully: %s\n", path)
				fileDeleted = true
				break // File deleted successfully, no need to check other paths
			} else {
				fmt.Printf("Failed to delete file: %s, error: %s\n", path, err.Error())
			}
		}
	}

	// If the file was not deleted from any location, log a message
	if !fileDeleted {
		fmt.Printf("File not found in any expected location. Removing metadata only.\n")
	}

	// Remove the metadata entry from the list
	if err := h.deleteMetadataEntry(metadataList, metadataFile, fileId); err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to delete metadata entry", err)
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "File deleted successfully"})
}

func (h *UploadHandler) deleteMetadataEntry(metadataList []models.FileMetadata, metadataFile string, fileId string) error {
	// Find the index of the metadata entry to delete
	indexToDelete := -1
	for i, metadata := range metadataList {
		if metadata.UploadID == fileId {
			indexToDelete = i
			break
		}
	}

	// If the metadata entry was found, remove it from the list
	if indexToDelete != -1 {
		// Remove the metadata entry from the list
		metadataList = append(metadataList[:indexToDelete], metadataList[indexToDelete+1:]...)

		// Save the updated metadata to the file
		if err := h.saveAllMetadataToFile(metadataList, metadataFile); err != nil {
			return fmt.Errorf("failed to save updated metadata: %w", err)
		}
	}

	return nil
}

func (h *UploadHandler) loadMetadataFromFile(metadataFile string) ([]models.FileMetadata, error) {
	var metadataList []models.FileMetadata

	file, err := os.Open(metadataFile)
	if err != nil {
		if os.IsNotExist(err) {
			// Jika file tidak ada, kembalikan list kosong
			return []models.FileMetadata{}, nil
		}
		return nil, fmt.Errorf("failed to open metadata file: %w", err)
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	if fileInfo.Size() > 0 {
		decoder := json.NewDecoder(file)
		err = decoder.Decode(&metadataList)
		if err != nil {
			return nil, fmt.Errorf("failed to decode existing metadata: %w", err)
		}
	}

	return metadataList, nil
}

func (h *UploadHandler) saveAllMetadataToFile(metadataList []models.FileMetadata, metadataFile string) error {
	data, err := json.MarshalIndent(metadataList, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal metadata to JSON: %w", err)
	}

	file, err := os.Create(metadataFile)
	if err != nil {
		return fmt.Errorf("failed to create metadata file: %w", err)
	}
	defer file.Close()

	_, err = file.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write metadata to file: %w", err)
	}

	return nil
}

// ListFiles mengembalikan daftar file metadata
func (h *UploadHandler) ListFiles(c echo.Context) error {
	// Jalankan checkAndMoveFiles sebelum menampilkan daftar file
	h.checkAndMoveFilesForAllChannels()

	metadataFile := filepath.Join(h.uploadDir, "metadata.json")

	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		return h.errorResponse(c, http.StatusInternalServerError, "Failed to load metadata", err)
	}

	//Prepare the file metadata list
	fileInfoList := make([]map[string]interface{}, len(metadataList))
	for i, metadata := range metadataList {
		fileInfoList[i] = map[string]interface{}{
			"Id":            metadata.UploadID, // Menggunakan UploadID sebagai ID file
			"original_name": metadata.FileName,
			"size":          metadata.FileSize,
			"duration":      metadata.Duration,
			"channel_id":    metadata.ChannelID, // Menyertakan channel_id
		}
	}

	return c.JSON(http.StatusOK, fileInfoList)
}

// checkAndMoveFilesForAllChannels memanggil checkAndMoveFiles untuk setiap channel ID yang ditemukan dalam metadata.
func (h *UploadHandler) checkAndMoveFilesForAllChannels() {
	metadataFile := filepath.Join(h.uploadDir, "metadata.json")

	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		fmt.Printf("Error loading metadata: %s\n", err)
		return
	}

	// Gunakan map untuk melacak channel ID yang sudah diproses untuk menghindari pemrosesan berulang
	processedChannels := make(map[string]bool)

	for _, metadata := range metadataList {
		channelID := metadata.ChannelID
		if channelID != "" && !processedChannels[channelID] {
			// Periksa dan pindahkan file untuk channel ID ini (dalam goroutine)
			go h.checkAndMoveFilesAndUpdateMetadata(channelID, metadata.UploadID, metadata.FileName) // Mengirim UploadID dan FileName
			processedChannels[channelID] = true // Tandai channel ID sebagai sudah diproses
		}
	}
}

// checkAndMoveFiles memeriksa keberadaan channelID di db.json dan memindahkan file jika tidak ada.
func (h *UploadHandler) checkAndMoveFiles(channelID string) {
	if channelID == "" {
		fmt.Println("Channel ID is empty, skipping check.")
		return
	}

	dbPath := filepath.Join(h.configDir, "db.json")

	exists, err := h.channelIDExistsInDB(dbPath, channelID)
	if err != nil {
		fmt.Printf("Error checking channel ID in db.json: %s\n", err)
		return
	}

	if !exists {
		fmt.Printf("Channel ID %s not found in db.json, moving files.\n", channelID)
		h.moveFilesToUploads(channelID)
	} else {
		fmt.Printf("Channel ID %s found in db.json, no action needed.\n", channelID)
	}
}

// channelIDExistsInDB memeriksa apakah channelID ada di db.json.
func (h *UploadHandler) channelIDExistsInDB(dbPath string, channelID string) (bool, error) {
	file, err := os.Open(dbPath)
	if err != nil {
		return false, fmt.Errorf("failed to open db.json: %w", err)
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	var data map[string]interface{}
	err = decoder.Decode(&data)
	if err != nil {
		return false, fmt.Errorf("failed to decode db.json: %w", err)
	}

	processData, ok := data["process"].(map[string]interface{})
	if !ok {
		return false, fmt.Errorf("invalid db.json structure: missing 'process' key")
	}

	for _, process := range processData {
		processMap, ok := process.(map[string]interface{})
		if !ok {
			continue // Skip jika bukan map
		}
		reference, ok := processMap["reference"].(string)
		if ok && reference == channelID {
			return true, nil
		}
	}

	return false, nil
}

// moveFilesToUploads memindahkan file dari direktori channelID ke direktori uploads.
func (h *UploadHandler) moveFilesToUploads(channelID string) error {
	channelDir := filepath.Join("data", "channels", channelID)
	uploadDir := h.uploadDir // Menggunakan direktori upload yang dikonfigurasi

	// Pastikan direktori channel ada
	if _, err := os.Stat(channelDir); os.IsNotExist(err) {
		fmt.Printf("Channel directory %s does not exist, skipping move.\n", channelDir)
		return nil
	}

	// Pindahkan videoloop.source
	sourceFile := filepath.Join(channelDir, "videoloop.source")
	if _, err := os.Stat(sourceFile); !os.IsNotExist(err) {
		newPath := filepath.Join(uploadDir, filepath.Base(sourceFile)) // Simpan dengan nama yang sama di folder upload
		err := os.Rename(sourceFile, newPath)
		if err != nil {
			fmt.Printf("Failed to move %s to %s: %s\n", sourceFile, newPath, err)
			return err
		}
		fmt.Printf("Moved %s to %s\n", sourceFile, newPath)
	}

	// Hapus channelID.html jika ada
	htmlFile := filepath.Join("data", channelID+".html")
	if _, err := os.Stat(htmlFile); !os.IsNotExist(err) {
		err := os.Remove(htmlFile)
		if err != nil {
			fmt.Printf("Failed to delete %s: %s\n", htmlFile, err)
			return err
		}
		fmt.Printf("Deleted %s\n", htmlFile)
	}

	// Hapus direktori channel
	err := os.RemoveAll(channelDir)
	if err != nil {
		fmt.Printf("Failed to remove channel directory %s: %s\n", channelDir, err)
		return err
	}
	fmt.Printf("Removed channel directory %s\n", channelDir)

	return nil
}

// checkAndMoveFilesAndUpdateMetadata menggabungkan pemeriksaan, pemindahan file, dan pembaruan metadata.
func (h *UploadHandler) checkAndMoveFilesAndUpdateMetadata(channelID string, uploadID string, originalFileName string) {
	if channelID == "" {
		fmt.Println("Channel ID is empty, skipping check.")
		return
	}

	dbPath := filepath.Join(h.configDir, "db.json")

	exists, err := h.channelIDExistsInDB(dbPath, channelID)
	if err != nil {
		fmt.Printf("Error checking channel ID in db.json: %s\n", err)
		return
	}

	if !exists {
		fmt.Printf("Channel ID %s not found in db.json, moving files and updating metadata.\n", channelID)
		h.moveFilesAndUpdateMetadata(channelID, uploadID, originalFileName) // Panggil fungsi baru
	} else {
		fmt.Printf("Channel ID %s found in db.json, no action needed.\n", channelID)
	}
}

// moveFilesAndUpdateMetadata memindahkan file, menghapus direktori, dan memperbarui metadata.
func (h *UploadHandler) moveFilesAndUpdateMetadata(channelID string, uploadID string, originalFileName string) {
	channelDir := filepath.Join("data", "channels", channelID)
	uploadDir := h.uploadDir

	// Pastikan direktori channel ada
	if _, err := os.Stat(channelDir); os.IsNotExist(err) {
		fmt.Printf("Channel directory %s does not exist, skipping move.\n", channelDir)
		h.updateMetadata(uploadID, originalFileName) // Tetap update metadata meskipun tidak ada file
		return
	}

	// Pindahkan videoloop.source
	sourceFile := filepath.Join(channelDir, "videoloop.source")
	newPath := filepath.Join(uploadDir, originalFileName) // Gunakan nama asli

	if _, err := os.Stat(sourceFile); !os.IsNotExist(err) {
		err := os.Rename(sourceFile, newPath)
		if err != nil {
			fmt.Printf("Failed to move %s to %s: %s\n", sourceFile, newPath, err)
		} else {
			fmt.Printf("Moved %s to %s\n", sourceFile, newPath)
		}
	}

	// Hapus channelID.html jika ada
	htmlFile := filepath.Join("data", channelID+".html")
	if _, err := os.Stat(htmlFile); !os.IsNotExist(err) {
		err := os.Remove(htmlFile)
		if err != nil {
			fmt.Printf("Failed to delete %s: %s\n", htmlFile, err)
			
		} else {
			fmt.Printf("Deleted %s\n", htmlFile)
		}
	}

	// Hapus direktori channel
	err := os.RemoveAll(channelDir)
	if err != nil {
		fmt.Printf("Failed to remove channel directory %s: %s\n", channelDir, err)
	} else {
		fmt.Printf("Removed channel directory %s\n", channelDir)
	}

	// Update Metadata (selalu dilakukan setelah pemindahan dan penghapusan)
	h.updateMetadata(uploadID, originalFileName)
}

// updateMetadata memperbarui metadata: menghapus channel ID dan mengganti nama file.
func (h *UploadHandler) updateMetadata(uploadID string, originalFileName string) error {
	metadataFile := filepath.Join(h.uploadDir, "metadata.json")

	metadataList, err := h.loadMetadataFromFile(metadataFile)
	if err != nil {
		return fmt.Errorf("failed to load metadata: %w", err)
	}

	updated := false
	for i, metadata := range metadataList {
		if metadata.UploadID == uploadID {
			metadataList[i].ChannelID = "" // Hapus channel ID
			metadataList[i].FileName = originalFileName
			metadataList[i].FilePath = filepath.Join(h.uploadDir, originalFileName) // Update path

			updated = true
			break // Asumsikan hanya ada satu entri metadata per UploadID
		}
	}

	if updated {
		if err := h.saveAllMetadataToFile(metadataList, metadataFile); err != nil {
			return fmt.Errorf("failed to save updated metadata: %w", err)
		}
		fmt.Printf("Metadata updated for UploadID %s\n", uploadID)
	} else {
		fmt.Printf("Metadata not found for UploadID %s\n", uploadID)
	}

	return nil
}