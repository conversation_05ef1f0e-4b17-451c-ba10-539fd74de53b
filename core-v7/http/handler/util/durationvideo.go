package util

import (
	"encoding/binary"
	"fmt"
	"io"
)

const (
	BoxHeaderSize = int64(8)
)

type MoovBox struct {
	Mvhd *MvhdBox
}

type MvhdBox struct {
	Timescale uint32
	Duration  uint32
}

// GetVideoDurationSeconds menggunakan parsing manual untuk mendapatkan durasi video.
func GetVideoDurationSeconds(reader io.ReaderAt, size int64) (float64, error) {
	// Cari 'moov' atom
	moovAtom, err := findAtom(reader, size, "moov")
	if err != nil {
		return 0, fmt.Errorf("failed to find moov atom: %w", err)
	}

	// Cari 'mvhd' atom di dalam 'moov'
	mvhdAtom, err := findAtom(reader, size, "mvhd", moovAtom.Start+BoxHeaderSize, moovAtom.Size-BoxHeaderSize)
	if err != nil {
		return 0, fmt.<PERSON><PERSON><PERSON>("failed to find mvhd atom: %w", err)
	}

	// Baca data 'mvhd'
	mvhdData := make([]byte, mvhdAtom.Size-BoxHeaderSize)
	_, err = reader.ReadAt(mvhdData, mvhdAtom.Start+BoxHeaderSize)
	if err != nil {
		return 0, fmt.Errorf("failed to read mvhd data: %w", err)
	}

	// Parse data 'mvhd'
	timescale := binary.BigEndian.Uint32(mvhdData[12:16])
	duration := binary.BigEndian.Uint32(mvhdData[16:20])

	return float64(duration) / float64(timescale), nil
}

// GetVideoDuration menggunakan parsing manual untuk mendapatkan durasi video.
// Mengembalikan durasi dalam format HH:MM:SS.
func GetVideoDuration(reader io.ReaderAt, size int64) (string, error) {
	duration, err := GetVideoDurationSeconds(reader, size)
	if err != nil {
		return "", err
	}

	// Format durasi menjadi HH:MM:SS
	hours := int(duration / 3600)
	minutes := int((duration - float64(hours*3600)) / 60)
	seconds := int(duration - float64(hours*3600) - float64(minutes*60))

	return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds), nil
}

// Atom merepresentasikan sebuah atom MP4
type Atom struct {
	Name  string
	Size  int64
	Start int64
}

// findAtom mencari atom dengan nama yang diberikan dalam file MP4
func findAtom(reader io.ReaderAt, fileSize int64, name string, start ...int64) (Atom, error) {
	var offset int64
	if len(start) > 0 {
		offset = start[0]
	}

	var n int64 = 4096 //4KB chunk
	buf := make([]byte, n)

	for {
		read, err := reader.ReadAt(buf, offset)
		if err != nil && err != io.EOF {
			return Atom{}, fmt.Errorf("failed to read file: %w", err)
		}

		if read == 0 {
			break // End of file
		}

		for i := 0; i <= read-int(BoxHeaderSize); i++ {
			atomSize := binary.BigEndian.Uint32(buf[i : i+4])
			atomName := string(buf[i+4 : i+8])

			if atomName == name {
				return Atom{
					Name:  atomName,
					Size:  int64(atomSize),
					Start: offset + int64(i),
				}, nil
			}
		}

		if err == io.EOF {
			break // End of file
		}

		offset += n

		// Safety check to prevent infinite loops
		if offset >= fileSize {
			break
		}
	}

	return Atom{}, fmt.Errorf("atom '%s' not found", name)
}