package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.36

import (
	"context"
	"time"

	"github.com/datarhei/core/v16/app"
	"github.com/datarhei/core/v16/http/graph/models"
	"github.com/datarhei/core/v16/http/graph/scalars"
)

// About is the resolver for the about field.
func (r *queryResolver) About(ctx context.Context) (*models.About, error) {
	createdAt := r.Restream.CreatedAt()

	about := &models.About{
		App:           app.Name,
		ID:            r.Restream.ID(),
		Name:          r.Restream.Name(),
		CreatedAt:     createdAt,
		UptimeSeconds: scalars.Uint64(time.Since(createdAt).Seconds()),
		Version: &models.AboutVersion{
			Number:           app.Version.String(),
			RepositoryCommit: app.Commit,
			RepositoryBranch: app.Branch,
			BuildDate:        app.Build,
			Arch:             app.Arch,
			Compiler:         app.Compiler,
		},
	}

	return about, nil
}
