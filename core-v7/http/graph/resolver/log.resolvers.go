package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.36

import (
	"context"
	"strings"

	"github.com/datarhei/core/v16/log"
)

// Log is the resolver for the log field.
func (r *queryResolver) Log(ctx context.Context) ([]string, error) {
	if r.LogBuffer == nil {
		r.LogBuffer = log.NewBufferWriter(log.Lsilent, 1)
	}

	events := r.LogBuffer.Events()

	formatter := log.NewConsoleFormatter(false)

	log := make([]string, len(events))

	for i, e := range events {
		log[i] = strings.TrimSpace(formatter.String(e))
	}

	return log, nil
}
