package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.36

import (
	"context"

	"github.com/datarhei/core/v16/http/graph/models"
)

// Processes is the resolver for the processes field.
func (r *queryResolver) Processes(ctx context.Context) ([]*models.Process, error) {
	ids := r.Restream.GetProcessIDs("", "")

	procs := []*models.Process{}

	for _, id := range ids {
		p, err := r.getProcess(id)
		if err != nil {
			return nil, err
		}

		procs = append(procs, p)
	}

	return procs, nil
}

// Process is the resolver for the process field.
func (r *queryResolver) Process(ctx context.Context, id string) (*models.Process, error) {
	return r.getProcess(id)
}

// Probe is the resolver for the probe field.
func (r *queryResolver) Probe(ctx context.Context, id string) (*models.Probe, error) {
	probe := r.Restream.Probe(id)

	p := &models.Probe{}
	p.UnmarshalRestream(probe)

	return p, nil
}
